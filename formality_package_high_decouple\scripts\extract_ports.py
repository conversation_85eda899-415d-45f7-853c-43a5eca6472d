#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verilog模块端口自动提取脚本
输入：Verilog文件、模块名
输出：端口列表（方向、类型、名称、宽度）
作者：AI助手
"""
import re
import sys
import argparse

def read_file_lines(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return f.readlines()

def extract_module_ports(lines, module_name):
    """
    提取指定模块的端口列表
    """
    ports = []
    in_module = False
    port_lines = []
    for idx, line in enumerate(lines):
        if re.match(rf'\s*module\s+{module_name}\b', line):
            in_module = True
            # 端口列表可能在同一行或多行
            l = line[line.find('(')+1:] if '(' in line else ''
            if l.strip():
                port_lines.append(l)
            continue
        if in_module:
            if ');' in line:
                l = line[:line.find(');')]
                if l.strip():
                    port_lines.append(l)
                break
            else:
                port_lines.append(line)
    # 合并端口声明
    port_text = ''.join(port_lines)
    # 分割每个端口
    port_items = [p.strip() for p in port_text.split(',') if p.strip()]
    # 逐个端口正则提取
    for p in port_items:
        # 匹配方向、类型、宽度、名称
        m = re.match(r'(input|output|inout)\s*(wire|reg)?\s*(\[[^\]]+\])?\s*([a-zA-Z_][\w]*)', p)
        if m:
            direction = m.group(1)
            vtype = m.group(2) if m.group(2) else ''
            width = m.group(3) if m.group(3) else ''
            name = m.group(4)
            ports.append({'dir': direction, 'type': vtype, 'width': width, 'name': name})
        else:
            # 可能是只写了端口名，类型在后面声明
            name = p.split()[-1]
            ports.append({'dir': '', 'type': '', 'width': '', 'name': name})
    return ports

def print_ports(ports):
    print('方向\t类型\t宽度\t名称')
    for p in ports:
        print(f"{p['dir']}\t{p['type']}\t{p['width']}\t{p['name']}")

def save_ports(ports, filename='ports.txt'):
    with open(filename, 'w', encoding='utf-8') as f:
        f.write('方向\t类型\t宽度\t名称\n')
        for p in ports:
            f.write(f"{p['dir']}\t{p['type']}\t{p['width']}\t{p['name']}\n")

def main():
    parser = argparse.ArgumentParser(description='自动提取Verilog模块端口列表')
    parser.add_argument('filename', help='Verilog文件')
    parser.add_argument('modname', help='模块名')
    args = parser.parse_args()

    lines = read_file_lines(args.filename)
    ports = extract_module_ports(lines, args.modname)
    print_ports(ports)
    save_ports(ports)
    print(f'端口信息已保存到 ports.txt')

if __name__ == '__main__':
    main() 