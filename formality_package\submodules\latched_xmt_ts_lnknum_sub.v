module latched_xmt_ts_lnknum_sub (
    input wire sticky_rst_n,
    input wire TP,
    input wire cfg_link_num,
    input wire latched_smlh_link_up,
    input wire smlh_link_up,
    input wire xmt_ts_lnknum,
    output reg [N-1:0] latched_xmt_ts_lnknum // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge sticky_rst_n ) begin : latched_xmt_ts_lnknum_PROC
        if ( ~sticky_rst_n ) begin
            latched_xmt_ts_lnknum <= #TP 0;
        end else if ( ~latched_smlh_link_up ) begin
            latched_xmt_ts_lnknum <= #TP cfg_link_num;
        end else if ( smlh_link_up ) begin
            latched_xmt_ts_lnknum <= #TP xmt_ts_lnknum;
        end
    end // latched_xmt_ts_lnknum_PROC


endmodule
