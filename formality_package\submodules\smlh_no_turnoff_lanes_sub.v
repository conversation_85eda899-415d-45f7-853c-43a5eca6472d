module smlh_no_turnoff_lanes_sub (
    input wire core_rst_n,
    input wire TP,
    input wire h0001,
    input wire h0003,
    input wire h000f,
    input wire h00ff,
    input wire hffff,
    input wire linkup_link_mode,
    input wire smlh_link_up,
    output reg [N-1:0] smlh_no_turnoff_lanes // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : smlh_no_turnoff_lanes_PROC
        if ( ~core_rst_n )
            smlh_no_turnoff_lanes <= #TP 0;
        else if ( smlh_link_up )
            smlh_no_turnoff_lanes <= #TP 
                                         linkup_link_mode[4] ? 'hffff :
                                         linkup_link_mode[3] ? 'h00ff :
                                         linkup_link_mode[2] ? 'h000f :
                                         linkup_link_mode[1] ? 'h0003 :
                                                               'h0001;
    end


endmodule
