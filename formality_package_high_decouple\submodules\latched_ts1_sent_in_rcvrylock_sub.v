module latched_ts1_sent_in_rcvrylock_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear_common_mode,
    input wire xmtbyte_ts1_sent,
    output reg [N-1:0] latched_ts1_sent_in_rcvrylock // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : latched_ts1_sent_in_rcvrylock_PROC
        if ( ~core_rst_n ) begin
            latched_ts1_sent_in_rcvrylock <= #TP 0;
        end else if ( clear_common_mode ) begin
            latched_ts1_sent_in_rcvrylock <= #TP 0;
        end else if ( |xmtbyte_ts1_sent ) begin
            latched_ts1_sent_in_rcvrylock <= #TP 1;
        end
    end //latched_ts1_sent_in_rcvrylock_PROC


endmodule
