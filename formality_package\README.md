# Verilog重构项目 - Formality验证包

## 项目概述
本项目将Verilog代码中的always块重构为独立的子模块，以提高代码的模块化程度和可维护性。

## 文件结构
`
formality_package/
 original/           # 原始设计文件
    EPX16_smlh_ltssm-7854.sv
 replaced/           # 重构后设计文件  
    EPX16_smlh_ltssm-7854_replaced.sv
 submodules/         # 提取的子模块文件
    *.v            # 各个always块对应的子模块
    *.sv           # SystemVerilog子模块
 scripts/            # 相关脚本
    analyze_always_blocks.py    # always块分析脚本
    batch_replace_always.py     # 批量替换脚本
    gen_always_to_submodule.py  # 子模块生成脚本
    ...
 run_formality.tcl   # Formality验证脚本
 log.txt            # 分析日志（如果存在）
 README.md          # 本文件
`

## 使用方法

### 1. 运行Formality验证
`ash
formality -f run_formality.tcl
`

### 2. 查看验证结果
- erification.rpt - 主要验证报告
- ailing_points.rpt - 失败点报告（如果有）
- borted.rpt - 中止点报告（如果有）
- unverified.rpt - 未验证点报告（如果有）

### 3. 调试（如果验证失败）
`ash
formality
restore_session formality_debug.fss
# 进行交互式调试
`

## 重构说明
1. **原始设计**: 包含284个always块的单一模块
2. **重构设计**: 将249个always块提取为独立子模块，主模块通过实例化调用
3. **等价性**: 重构前后的功能应该完全等价

## 验证要点
- 所有输入输出端口保持一致
- 时序行为保持一致  
- 组合逻辑功能保持一致
- 复位行为保持一致

## 注意事项
1. 确保所有子模块文件都在submodules目录中
2. 检查模块名和文件名的一致性
3. 验证时钟和复位信号的连接
4. 注意参数传递的正确性

## 故障排除
如果验证失败，请检查：
1. 端口连接是否正确
2. 信号位宽是否匹配
3. 时钟域是否一致
4. 复位逻辑是否相同
5. 参数定义是否一致

## 统计信息
- 原始always块数量: 284个
- 成功替换的always块: 249个
- 生成的子模块数量: 177个
- 替换成功率: 87.7%

生成时间: 2025-07-30
