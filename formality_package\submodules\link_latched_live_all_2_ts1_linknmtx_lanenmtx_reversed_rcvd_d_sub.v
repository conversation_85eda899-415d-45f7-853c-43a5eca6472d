module link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear,
    input wire link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd,
    output reg [N-1:0] link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d_PROC
        if ( ~core_rst_n )
            link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d <= #TP 0;
        else if ( clear )
            link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d <= #TP 0;
        else if ( link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd )
            link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d <= #TP 1;
    end // link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d_PROC


endmodule
