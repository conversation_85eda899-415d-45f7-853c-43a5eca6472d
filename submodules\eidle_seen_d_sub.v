module eidle_seen_d_sub (
    input wire core_rst_n,
    input wire S_COMPL_TX_COMPLIANCE,
    input wire TP,
    input wire curnt_compliance_state,
    input wire rmlh_rcvd_eidle_set,
    output reg [N-1:0] eidle_seen_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : eidle_seen_d_PROC
        if ( ~core_rst_n ) begin
            eidle_seen_d <= #TP 0;
        end else if ( (curnt_compliance_state != S_COMPL_TX_COMPLIANCE) ) begin
            eidle_seen_d <= #TP 0;
        end else if ( rmlh_rcvd_eidle_set ) begin
            eidle_seen_d <= #TP 1;
        end
    end // eidle_seen_d_PROC


endmodule
