module hold_current_data_rate_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear,
    input wire current_data_rate,
    output reg [N-1:0] hold_current_data_rate // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : hold_current_data_rate_PROC
        if ( ~core_rst_n )
            hold_current_data_rate <= #TP 0;
        else if ( clear )
            hold_current_data_rate <= #TP current_data_rate;
    end // hold_current_data_rate_PROC


endmodule
