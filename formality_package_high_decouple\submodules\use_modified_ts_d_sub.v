module use_modified_ts_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire use_modified_ts,
    output reg [N-1:0] use_modified_ts_d // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : use_modified_ts_d_PROC
        if ( ~core_rst_n )
            use_modified_ts_d <= #TP 1'b0;
        else
            use_modified_ts_d <= #TP use_modified_ts;
    end // use_modified_ts_d_PROC


endmodule
