module always_block_171_sub (
    input wire core_rst_n,
    input wire TP,
    input wire rdlh_rcvd_dllp,
    input wire smlh_link_up,
    output reg [N-1:0] latched_only_rdlh_rcvd_dllp // TODO: 请根据实际宽度修改,
    output reg [N-1:0] latched_rdlh_rcvd_dllp // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : latched_rdlh_rcvd_dllp_PROC
        if ( ~core_rst_n ) begin
            latched_rdlh_rcvd_dllp <= #TP 0;
            latched_only_rdlh_rcvd_dllp <= #TP 0;
        end else if ( ~smlh_link_up) begin
            latched_rdlh_rcvd_dllp <= #TP 0;
            latched_only_rdlh_rcvd_dllp <= #TP 0;
        end else begin
            if ( rdlh_rcvd_dllp) begin // for cxl EP device, the first received is io or mem-cache ALMP requests rather than PCIe's DLLP. used to clear the eq pending
                latched_rdlh_rcvd_dllp <= #TP 1;
            end

            if ( rdlh_rcvd_dllp ) begin
                latched_only_rdlh_rcvd_dllp <= #TP 1; // for cxl.io usp to send dllp after rcvd dllp
            end


endmodule
