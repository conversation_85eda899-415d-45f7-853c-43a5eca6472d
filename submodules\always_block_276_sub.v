module always_block_276_sub (
    input wire S_RCVRY_LOCK,
    input wire b0,
    input wire r_ltssm_rcvr_err_rpt_en,
    output reg [N-1:0] ltssm // TODO: 请根据实际宽度修改,
    output reg [N-1:0] ltssm_rcvr_err_rpt_en // TODO: 请根据实际宽度修改
);

    always @( r_ltssm_rcvr_err_rpt_en or ltssm ) begin
      if ( ltssm == S_RCVRY_LOCK )
        ltssm_rcvr_err_rpt_en = 1'b0;
      else
        ltssm_rcvr_err_rpt_en = r_ltssm_rcvr_err_rpt_en;
    end


endmodule
