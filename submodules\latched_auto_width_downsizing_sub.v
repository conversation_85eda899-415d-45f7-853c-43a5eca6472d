module latched_auto_width_downsizing_sub (
    input wire core_rst_n,
    input wire TP,
    input wire directed_link_width_change_down,
    input wire smlh_link_up,
    output reg [N-1:0] latched_auto_width_downsizing // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : latched_auto_width_downsizing_PROC
        if ( ~core_rst_n )
            latched_auto_width_downsizing <= #TP 0;
        else if ( !smlh_link_up )
            latched_auto_width_downsizing <= #TP 0;
        else
            latched_auto_width_downsizing <= #TP latched_auto_width_downsizing | directed_link_width_change_down;
    end


endmodule
