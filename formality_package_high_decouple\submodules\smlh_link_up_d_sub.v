module smlh_link_up_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire smlh_link_up,
    output reg [N-1:0] smlh_link_up_d // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : smlh_link_up_d_PROC
        if ( ~core_rst_n )
            smlh_link_up_d <= #TP 1'b0;
        else
            smlh_link_up_d <= #TP smlh_link_up;
    end // smlh_link_up_d_PROC


endmodule
