module rcvd_valid_eidle_set_sub (
    input wire act_rmlh_rcvd_eidle_set,
    input wire active_nb,
    input wire eidle_continuity_cnt,
    input wire rcvd_eidle_cnt,
    output reg [N-1:0] rcvd_valid_eidle_set // TODO: 请根据实际宽度修改
);

    always @( act_rmlh_rcvd_eidle_set or rcvd_eidle_cnt or eidle_continuity_cnt or active_nb ) begin : EIDLE_VALID
      rcvd_valid_eidle_set = 0;
        rcvd_valid_eidle_set[0] = act_rmlh_rcvd_eidle_set[0] && ( (rcvd_eidle_cnt[3:0] == 0)
                                     || (eidle_continuity_cnt[2:0] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[1] = act_rmlh_rcvd_eidle_set[1] && ( (rcvd_eidle_cnt[7:4] == 0)
                                     || (eidle_continuity_cnt[5:3] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[2] = act_rmlh_rcvd_eidle_set[2] && ( (rcvd_eidle_cnt[11:8] == 0)
                                     || (eidle_continuity_cnt[8:6] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[3] = act_rmlh_rcvd_eidle_set[3] && ( (rcvd_eidle_cnt[15:12] == 0)
                                     || (eidle_continuity_cnt[11:9] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[4] = act_rmlh_rcvd_eidle_set[4] && ( (rcvd_eidle_cnt[19:16] == 0)
                                     || (eidle_continuity_cnt[14:12] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[5] = act_rmlh_rcvd_eidle_set[5] && ( (rcvd_eidle_cnt[23:20] == 0)
                                     || (eidle_continuity_cnt[17:15] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[6] = act_rmlh_rcvd_eidle_set[6] && ( (rcvd_eidle_cnt[27:24] == 0)
                                     || (eidle_continuity_cnt[20:18] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[7] = act_rmlh_rcvd_eidle_set[7] && ( (rcvd_eidle_cnt[31:28] == 0)
                                     || (eidle_continuity_cnt[23:21] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[8] = act_rmlh_rcvd_eidle_set[8] && ( (rcvd_eidle_cnt[35:32] == 0)
                                     || (eidle_continuity_cnt[26:24] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[9] = act_rmlh_rcvd_eidle_set[9] && ( (rcvd_eidle_cnt[39:36] == 0)
                                     || (eidle_continuity_cnt[29:27] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[10] = act_rmlh_rcvd_eidle_set[10] && ( (rcvd_eidle_cnt[43:40] == 0)
                                     || (eidle_continuity_cnt[32:30] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[11] = act_rmlh_rcvd_eidle_set[11] && ( (rcvd_eidle_cnt[47:44] == 0)
                                     || (eidle_continuity_cnt[35:33] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[12] = act_rmlh_rcvd_eidle_set[12] && ( (rcvd_eidle_cnt[51:48] == 0)
                                     || (eidle_continuity_cnt[38:36] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[13] = act_rmlh_rcvd_eidle_set[13] && ( (rcvd_eidle_cnt[55:52] == 0)
                                     || (eidle_continuity_cnt[41:39] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[14] = act_rmlh_rcvd_eidle_set[14] && ( (rcvd_eidle_cnt[59:56] == 0)
                                     || (eidle_continuity_cnt[44:42] == 0) || active_nb[2] );

        rcvd_valid_eidle_set[15] = act_rmlh_rcvd_eidle_set[15] && ( (rcvd_eidle_cnt[63:60] == 0)
                                     || (eidle_continuity_cnt[47:45] == 0) || active_nb[2] );
    end


endmodule
