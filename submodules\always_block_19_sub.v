module always_block_19_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b1,
    input wire int_bypass_gen3_eq,
    input wire int_bypass_gen4_eq,
    output reg [N-1:0] bypass_g3_eq // TODO: 请根据实际宽度修改,
    output reg [N-1:0] bypass_g4_eq // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : bypass_eq_PROC
        if ( ~core_rst_n ) begin
            bypass_g3_eq <= #TP 1'b1;
            bypass_g4_eq <= #TP 1'b1;
        end else begin
            bypass_g3_eq <= #TP int_bypass_gen3_eq;
            bypass_g4_eq <= #TP int_bypass_gen4_eq;
        end
    end // bypass_eq_PROC


endmodule
