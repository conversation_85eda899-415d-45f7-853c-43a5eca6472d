module soft_gen4_eq_phase_flag_ff_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire b1,
    input wire clr_soft_gen4_eq_phase_flag,
    input wire set_soft_gen4_eq_phase_flag,
    input wire soft_eq_phase_flag_nomask,
    output reg [N-1:0] soft_gen4_eq_phase_flag_ff // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : soft_gen4_eq_phase_flag_PROC
        if (!core_rst_n) begin
            soft_gen4_eq_phase_flag_ff <= #TP 1'b0;
        end else if( soft_eq_phase_flag_nomask ) begin
            if ( set_soft_gen4_eq_phase_flag )
                soft_gen4_eq_phase_flag_ff <= #TP 1'b1;
            else if( clr_soft_gen4_eq_phase_flag )
                soft_gen4_eq_phase_flag_ff <= #TP 1'b0;
        end
        else begin
            soft_gen4_eq_phase_flag_ff <= #TP 1'b0;
        end
    end // soft_gen4_eq_phase_flag_PROC


endmodule
