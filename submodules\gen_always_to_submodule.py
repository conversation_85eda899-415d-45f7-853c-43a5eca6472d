
# -*- coding: utf-8 -*-
"""
Verilog always块自动拆分为submodule的脚本
输入：源文件、always块起止行号、子模块名
输出：submodule代码、主模块实例化代码
作者：AI助手
"""
import re
import sys
import argparse
from collections import defaultdict

def read_file_lines(filename):
    """读取文件，尝试多种编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as f:
                return f.readlines()
        except UnicodeDecodeError:
            continue
    
    # 如果所有编码都失败，使用utf-8并忽略错误
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        return f.readlines()

def extract_always_block(lines, start, end):
    """
    提取指定行号的always块代码
    """
    return lines[start-1:end]

def extract_signals(always_lines):
    """
    简单正则提取左值(输出)、右值(输入)信号
    """
    text = ''.join(always_lines)
    # 提取左值（被赋值的reg）
    lefts = re.findall(r'([a-zA-Z_][\w\[\]]*)\s*(<=|=)', text)
    outputs = set([l[0].split('[')[0] for l in lefts])
    # 提取右值（赋值右侧、条件表达式等）
    rights = re.findall(r'(?:<=|=)\s*([^;]+);', text)
    inputs = set()
    for expr in rights:
        tokens = re.findall(r'\b([a-zA-Z_][\w]*)\b', expr)
        for t in tokens:
            if t not in outputs and not re.match(r'^(begin|if|else|case|end|for|while|posedge|negedge|or|and|not|xor|xnor|assign|wire|reg|input|output|parameter|localparam)$', t):
                inputs.add(t)
    # 还要提取if/else/条件中的信号
    conds = re.findall(r'if\s*\(([^\)]+)\)', text)
    for cond in conds:
        tokens = re.findall(r'\b([a-zA-Z_][\w]*)\b', cond)
        for t in tokens:
            if t not in outputs and not re.match(r'^(begin|if|else|case|end|for|while|posedge|negedge|or|and|not|xor|xnor|assign|wire|reg|input|output|parameter|localparam)$', t):
                inputs.add(t)
    return sorted(inputs), sorted(outputs)

def guess_clk_rst(inputs):
    """
    猜测时钟和复位信号
    """
    clk = None
    rst = None
    for s in inputs:
        if 'clk' in s:
            clk = s
        if 'rst' in s or 'reset' in s:
            rst = s
    return clk, rst

def gen_submodule_code(submod_name, clk, rst, inputs, outputs, always_lines):
    """
    生成submodule Verilog代码
    """
    # 端口列表
    port_lines = []
    if clk:
        port_lines.append(f'    input wire {clk}')
    if rst:
        port_lines.append(f'    input wire {rst}')
    for i in inputs:
        if i != clk and i != rst:
            port_lines.append(f'    input wire {i}')
    for o in outputs:
        port_lines.append(f'    output reg [N-1:0] {o} // TODO: 请根据实际宽度修改')
    port_str = ',\n'.join(port_lines)
    # always块内容缩进
    body = ''.join(['    '+l if l.strip() else l for l in always_lines])
    code = f'''module {submod_name} (
{port_str}
);

{body}

endmodule
'''
    return code

def gen_inst_code(submod_name, clk, rst, inputs, outputs):
    """
    生成主模块实例化代码
    """
    port_map = []
    if clk:
        port_map.append(f'.{clk}({clk})')
    if rst:
        port_map.append(f'.{rst}({rst})')
    for i in inputs:
        if i != clk and i != rst:
            port_map.append(f'.{i}({i})')
    for o in outputs:
        port_map.append(f'.{o}({o})')
    port_map_str = ',\n    '.join(port_map)
    code = f'''{submod_name} u_{submod_name} (
    {port_map_str}
);
'''
    return code

def main():
    parser = argparse.ArgumentParser(description='自动将Verilog always块拆分为submodule')
    parser.add_argument('filename', help='源Verilog文件')
    parser.add_argument('start', type=int, help='always块起始行号')
    parser.add_argument('end', type=int, help='always块结束行号')
    parser.add_argument('submod', help='子模块名称')
    args = parser.parse_args()

    lines = read_file_lines(args.filename)
    always_lines = extract_always_block(lines, args.start, args.end)
    inputs, outputs = extract_signals(always_lines)
    clk, rst = guess_clk_rst(inputs)
    submod_code = gen_submodule_code(args.submod, clk, rst, inputs, outputs, always_lines)
    inst_code = gen_inst_code(args.submod, clk, rst, inputs, outputs)

    print('=== 子模块代码 ===')
    print(submod_code)
    print('=== 实例化代码 ===')
    print(inst_code)

    # 保存到文件
    with open(f'{args.submod}.v', 'w', encoding='utf-8') as f:
        f.write(submod_code)
    with open('instantiation.txt', 'w', encoding='utf-8') as f:
        f.write(inst_code)

if __name__ == '__main__':
    main() 
