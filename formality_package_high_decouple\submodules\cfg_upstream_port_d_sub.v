module cfg_upstream_port_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire cfg_upstream_port,
    output reg [N-1:0] cfg_upstream_port_d // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : cfg_upstream_port_d_PROC
        if ( ~core_rst_n )
            cfg_upstream_port_d <= #TP 0;
        else
            cfg_upstream_port_d <= #TP cfg_upstream_port;
    end // cfg_upstream_port_d_PROC


endmodule
