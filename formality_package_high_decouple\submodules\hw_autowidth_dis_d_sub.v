module hw_autowidth_dis_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire cfg_hw_autowidth_dis,
    output reg [N-1:0] hw_autowidth_dis_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : hw_autowidth_dis_d_PROC
        if ( ~core_rst_n )
            hw_autowidth_dis_d <= #TP 0;
        else
            hw_autowidth_dis_d <= #TP cfg_hw_autowidth_dis;
    end


endmodule
