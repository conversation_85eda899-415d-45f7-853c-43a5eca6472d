module latched_eqctl_any_8eqts2_rcvd_sub (
    input wire core_rst_n,
    input wire S_RCVRY_RCVRCFG,
    input wire TP,
    input wire clear,
    input wire int_eqctl_any_8eqts2_rcvd,
    input wire lts_state,
    output reg [N-1:0] latched_eqctl_any_8eqts2_rcvd // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : eqctl_any_8eqts2_rcvd_r_PROC
        if ( !core_rst_n ) begin
            latched_eqctl_any_8eqts2_rcvd <= #TP 0;
        end else if ( lts_state != S_RCVRY_RCVRCFG ) begin
            latched_eqctl_any_8eqts2_rcvd <= #TP 0;
        end else if (int_eqctl_any_8eqts2_rcvd && !clear) begin
            latched_eqctl_any_8eqts2_rcvd <= #TP 1;
        end
    end


endmodule
