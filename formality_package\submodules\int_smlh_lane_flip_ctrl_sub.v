module int_smlh_lane_flip_ctrl_sub (
    input wire core_rst_n,
    input wire TP,
    input wire next_smlh_lane_flip_ctrl,
    output reg [N-1:0] int_smlh_lane_flip_ctrl // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : smlh_lane_flip_ctrl_PROC
        if (!core_rst_n)
            int_smlh_lane_flip_ctrl <= #TP 0;
        else
            int_smlh_lane_flip_ctrl <= #TP next_smlh_lane_flip_ctrl;
    end


endmodule
