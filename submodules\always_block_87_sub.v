module always_block_87_sub (
    input wire EPX16_CX_TIME_1MS,
    input wire EPX16_ZBITS,
    input wire always,
    input wire cfg_fast_link_mode,
    input wire compare_t_wd_pz,
    input wire fast_time_1ms,
    input wire ltssm_timer_24ms_cnt,
    input wire timer,
    output reg [N-1:0] cnt_i // TODO: 请根据实际宽度修改,
    output reg [N-1:0] timer_24ms_cnt_i // TODO: 请根据实际宽度修改
);

    always @(*) begin : timer_24ms_cnt_i_PROC
        integer cnt_i;
        timer_24ms_cnt_i = ltssm_timer_24ms_cnt;

        for ( cnt_i=0; cnt_i<=24; cnt_i=cnt_i+1 ) begin
            if ( cfg_fast_link_mode ) begin
                if ( compare_t_wd_pz({`EPX16_ZBITS,timer}, (cnt_i * fast_time_1ms)) )
                    timer_24ms_cnt_i = cnt_i;
            end else begin
                if ( compare_t_wd_pz({`EPX16_ZBITS,timer}, (cnt_i * `EPX16_CX_TIME_1MS)) )
                    timer_24ms_cnt_i = cnt_i;
            end //if ( cfg_fast_link_mode
        end //for ( cnt_i
    end //always @(*)


endmodule
