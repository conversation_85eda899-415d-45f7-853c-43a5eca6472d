module always_block_12_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire eqctl_precode_request,
    input wire link_pc_rcvd,
    output reg [N-1:0] eqctl_precode_request_d // TODO: 请根据实际宽度修改,
    output reg [N-1:0] link_pc_rcvd_d // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : link_pc_rcvd_d_PROC
        if ( ~core_rst_n ) begin
            link_pc_rcvd_d          <= #TP 1'b0;
            eqctl_precode_request_d <= #TP 1'b0;
        end else begin
            link_pc_rcvd_d          <= #TP link_pc_rcvd;
            eqctl_precode_request_d <= #TP eqctl_precode_request;
        end
    end // link_pc_rcvd_d_PROC


endmodule
