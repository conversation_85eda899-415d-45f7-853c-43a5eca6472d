module precoding_lat_en_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire precoding_lat_en_w,
    output reg [N-1:0] precoding_lat_en // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : precoding_lat_en_PROC
        if(~core_rst_n)
            precoding_lat_en <= #TP 1'b0;
        else
            precoding_lat_en <= #TP precoding_lat_en_w;
    end


endmodule
