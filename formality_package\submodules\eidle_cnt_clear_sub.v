module eidle_cnt_clear_sub (
    input wire act_rmlh_rcvd_eidle_set,
    input wire active_nb,
    input wire d0,
    input wire eidle_continuity_cnt,
    input wire rcvd_eidle_cnt,
    output reg [N-1:0] eidle_cnt_clear // TODO: 请根据实际宽度修改
);

    always @(active_nb or rcvd_eidle_cnt or act_rmlh_rcvd_eidle_set or eidle_continuity_cnt) begin : EIDLE_CNT_CLEAR
      eidle_cnt_clear = 0;

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[0] = active_nb[2] ? (|rcvd_eidle_cnt[3:0] && !act_rmlh_rcvd_eidle_set[0]) :
                                (eidle_continuity_cnt[2:0] == 3'd0) && !act_rmlh_rcvd_eidle_set[0];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[1] = active_nb[2] ? (|rcvd_eidle_cnt[7:4] && !act_rmlh_rcvd_eidle_set[1]) :
                                (eidle_continuity_cnt[5:3] == 3'd0) && !act_rmlh_rcvd_eidle_set[1];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[2] = active_nb[2] ? (|rcvd_eidle_cnt[11:8] && !act_rmlh_rcvd_eidle_set[2]) :
                                (eidle_continuity_cnt[8:6] == 3'd0) && !act_rmlh_rcvd_eidle_set[2];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[3] = active_nb[2] ? (|rcvd_eidle_cnt[15:12] && !act_rmlh_rcvd_eidle_set[3]) :
                                (eidle_continuity_cnt[11:9] == 3'd0) && !act_rmlh_rcvd_eidle_set[3];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[4] = active_nb[2] ? (|rcvd_eidle_cnt[19:16] && !act_rmlh_rcvd_eidle_set[4]) :
                                (eidle_continuity_cnt[14:12] == 3'd0) && !act_rmlh_rcvd_eidle_set[4];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[5] = active_nb[2] ? (|rcvd_eidle_cnt[23:20] && !act_rmlh_rcvd_eidle_set[5]) :
                                (eidle_continuity_cnt[17:15] == 3'd0) && !act_rmlh_rcvd_eidle_set[5];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[6] = active_nb[2] ? (|rcvd_eidle_cnt[27:24] && !act_rmlh_rcvd_eidle_set[6]) :
                                (eidle_continuity_cnt[20:18] == 3'd0) && !act_rmlh_rcvd_eidle_set[6];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[7] = active_nb[2] ? (|rcvd_eidle_cnt[31:28] && !act_rmlh_rcvd_eidle_set[7]) :
                                (eidle_continuity_cnt[23:21] == 3'd0) && !act_rmlh_rcvd_eidle_set[7];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[8] = active_nb[2] ? (|rcvd_eidle_cnt[35:32] && !act_rmlh_rcvd_eidle_set[8]) :
                                (eidle_continuity_cnt[26:24] == 3'd0) && !act_rmlh_rcvd_eidle_set[8];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[9] = active_nb[2] ? (|rcvd_eidle_cnt[39:36] && !act_rmlh_rcvd_eidle_set[9]) :
                                (eidle_continuity_cnt[29:27] == 3'd0) && !act_rmlh_rcvd_eidle_set[9];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[10] = active_nb[2] ? (|rcvd_eidle_cnt[43:40] && !act_rmlh_rcvd_eidle_set[10]) :
                                (eidle_continuity_cnt[32:30] == 3'd0) && !act_rmlh_rcvd_eidle_set[10];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[11] = active_nb[2] ? (|rcvd_eidle_cnt[47:44] && !act_rmlh_rcvd_eidle_set[11]) :
                                (eidle_continuity_cnt[35:33] == 3'd0) && !act_rmlh_rcvd_eidle_set[11];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[12] = active_nb[2] ? (|rcvd_eidle_cnt[51:48] && !act_rmlh_rcvd_eidle_set[12]) :
                                (eidle_continuity_cnt[38:36] == 3'd0) && !act_rmlh_rcvd_eidle_set[12];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[13] = active_nb[2] ? (|rcvd_eidle_cnt[55:52] && !act_rmlh_rcvd_eidle_set[13]) :
                                (eidle_continuity_cnt[41:39] == 3'd0) && !act_rmlh_rcvd_eidle_set[13];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[14] = active_nb[2] ? (|rcvd_eidle_cnt[59:56] && !act_rmlh_rcvd_eidle_set[14]) :
                                (eidle_continuity_cnt[44:42] == 3'd0) && !act_rmlh_rcvd_eidle_set[14];

        //if in 4S mode, clear on any interruption of act_rmlh_rcvd_eidle_set
        eidle_cnt_clear[15] = active_nb[2] ? (|rcvd_eidle_cnt[63:60] && !act_rmlh_rcvd_eidle_set[15]) :
                                (eidle_continuity_cnt[47:45] == 3'd0) && !act_rmlh_rcvd_eidle_set[15];
    end


endmodule
