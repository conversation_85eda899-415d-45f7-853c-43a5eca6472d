module timeout_24ms_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire timeout_24ms,
    output reg [N-1:0] timeout_24ms_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : timeout_24ms_d_PROC
        if (!core_rst_n)
            timeout_24ms_d <= #TP 0;
        else
            timeout_24ms_d <= #TP timeout_24ms;
    end // timeout_2ms_d_PROC


endmodule
