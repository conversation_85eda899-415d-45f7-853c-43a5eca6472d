module timeout_common_mode_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear_common_mode,
    input wire speed_clear,
    input wire timer_gr_eq_cfg_cmode,
    output reg [N-1:0] timeout_common_mode // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : timeout_common_mode_PROC
        if ( ~core_rst_n ) begin
            timeout_common_mode <= #TP 0;
        end else begin
            if ( clear_common_mode || speed_clear ) begin
                timeout_common_mode <= #TP 0;
            end else if ( timer_gr_eq_cfg_cmode ) begin
                timeout_common_mode <= #TP 1;
            end
        end
    end //timeout_common_mode_PROC


endmodule
