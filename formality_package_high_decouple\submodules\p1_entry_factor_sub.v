module p1_entry_factor_sub (
    output reg [N-1:0] p1_entry_factor // TODO: 请根据实际宽度修改
);

    always @(*) begin
        casez({p1_entry_factor_case})
            4'b11_??: p1_entry_factor = 4 ;  // 640ns
            4'b10_??: p1_entry_factor = 3 ;  // 320ns
            4'b01_??: p1_entry_factor = 2 ;  // 160ns
            4'b00_11: p1_entry_factor = 1 ;  // 80ns
            4'b00_10: p1_entry_factor = 0 ;  // 40ns
            4'b00_01: p1_entry_factor = 3 ;  // 320ns
            default : p1_entry_factor = 2 ;  // Default 160ns
        endcase


endmodule
