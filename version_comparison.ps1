﻿# 版本对比分析脚本
# 比较完整版本和高解耦版本的差异

Write-Host "=== Verilog重构项目版本对比分析 ===" -ForegroundColor Green

Write-Host "
📊 统计信息对比:" -ForegroundColor Yellow
Write-Host "原始always块总数: 284个" -ForegroundColor White

Write-Host "
🔵 完整版本 (verilog_refactor_formality_package.zip):" -ForegroundColor Cyan
Write-Host "  - 替换always块数量: 249个" -ForegroundColor White
Write-Host "  - 替换成功率: 87.7%" -ForegroundColor White
Write-Host "  - 风险等级: 中等" -ForegroundColor Yellow
Write-Host "  - 适用场景: 功能验证、性能优化" -ForegroundColor White

Write-Host "
🟢 高解耦版本 (verilog_refactor_high_decouple_formality_package.zip):" -ForegroundColor Green
Write-Host "  - 替换always块数量: 122个" -ForegroundColor White
Write-Host "  - 替换成功率: 43.0%" -ForegroundColor White
Write-Host "  - 风险等级: 最低" -ForegroundColor Green
Write-Host "  - 适用场景: 生产环境、关键系统" -ForegroundColor White

Write-Host "
📈 详细对比:" -ForegroundColor Yellow
Write-Host "" -ForegroundColor White
Write-Host " 版本             替换数量  替换率    风险等级  推荐场景     " -ForegroundColor White
Write-Host "" -ForegroundColor White
Write-Host " 完整版本         249/284   87.7%     中等      功能验证     " -ForegroundColor White
Write-Host " 高解耦版本       122/284   43.0%     最低      生产环境     " -ForegroundColor White
Write-Host "" -ForegroundColor White

Write-Host "
🎯 选择建议:" -ForegroundColor Yellow
Write-Host "  🔸 如果追求最大重构效果和性能优化  选择完整版本" -ForegroundColor Cyan
Write-Host "  🔸 如果追求最高安全性和稳定性  选择高解耦版本" -ForegroundColor Green
Write-Host "  🔸 建议先使用高解耦版本验证，再考虑完整版本" -ForegroundColor Yellow

Write-Host "
📁 文件信息:" -ForegroundColor Yellow
 = (Get-Item "verilog_refactor_formality_package.zip").Length
 = (Get-Item "verilog_refactor_high_decouple_formality_package.zip").Length
Write-Host "  完整版本包大小: 0 KB" -ForegroundColor White
Write-Host "  高解耦版本包大小: 0 KB" -ForegroundColor White

Write-Host "
✅ 两个版本都已准备就绪，可用于formality等价验证！" -ForegroundColor Green
