module pset_map_eieos_cnt_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear,
    input wire ltssm_cmd_send_eieos_for_pset_map,
    input wire xmtbyte_eies_sent,
    input wire xmtbyte_ts1_sent,
    input wire xmtbyte_ts2_sent,
    output reg [N-1:0] pset_map_eieos_cnt // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : pset_map_eieos_cnt_PROC
        if ( ~core_rst_n ) begin
            pset_map_eieos_cnt <= #TP 0;
        end else if ( clear || ~ltssm_cmd_send_eieos_for_pset_map ) begin
            pset_map_eieos_cnt <= #TP 0;
        end else if ( xmtbyte_eies_sent || xmtbyte_ts1_sent[0] || xmtbyte_ts2_sent[0]) begin
            pset_map_eieos_cnt <= #TP pset_map_eieos_cnt + 1;
        end


endmodule
