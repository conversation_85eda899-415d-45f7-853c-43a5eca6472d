module always_block_141_sub (
    input wire core_rst_n,
    input wire TP,
    output reg [N-1:0] ltssm_core_rst_n_release // TODO: 请根据实际宽度修改,
    output reg [N-1:0] ltssm_core_rst_n_release_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : ltssm_core_rst_n_release_PROC
        if ( ~core_rst_n ) begin
            ltssm_core_rst_n_release   <= #TP 0;
            ltssm_core_rst_n_release_d <= #TP 0;
        end else begin
            ltssm_core_rst_n_release   <= #TP 1;
            ltssm_core_rst_n_release_d <= #TP ltssm_core_rst_n_release;
        end
    end //ltssm_core_rst_n_release_PROC


endmodule
