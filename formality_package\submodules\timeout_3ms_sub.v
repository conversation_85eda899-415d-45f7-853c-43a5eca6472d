module timeout_3ms_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b1,
    input wire cfg_fast_link_mode,
    input wire clear,
    input wire compare_t_wd,
    input wire fast_time_3ms,
    input wire timer,
    output reg [N-1:0] timeout_3ms // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : timeout_3ms_PROC
        if (!core_rst_n)
            timeout_3ms  <= #TP 0;
        else if (clear)
            timeout_3ms  <= #TP 0;
        else if ( (cfg_fast_link_mode && (compare_t_wd(timer, fast_time_3ms))) || (~cfg_fast_link_mode && compare_t_wd(timer, `EPX16_CX_TIME_3MS)) )  
            timeout_3ms <= #TP 1'b1;
    end // timeout_3ms_PROC


endmodule
