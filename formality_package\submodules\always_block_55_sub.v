module always_block_55_sub (
    input wire EPX16_CX_TIME_24MS,
    input wire EPX16_CX_TIME_32MS,
    input wire b00,
    input wire fast_time_24ms,
    input wire fast_time_32ms,
    output reg [N-1:0] ext_fast_time_24ms // TODO: 请根据实际宽度修改,
    output reg [N-1:0] ext_fast_time_32ms // TODO: 请根据实际宽度修改,
    output reg [N-1:0] ext_time_24ms // TODO: 请根据实际宽度修改,
    output reg [N-1:0] ext_time_32ms // TODO: 请根据实际宽度修改
);

    always @(*) begin
        case(cdm_ras_des_ext_eq_to_factor)
            2: begin // (x10)
                ext_fast_time_24ms = fast_time_24ms*10;
                ext_fast_time_32ms = fast_time_24ms*9 + fast_time_32ms ; // 248ms (24ms x 10 + 8ms)
                ext_time_24ms      = (`EPX16_CX_TIME_24MS*10);
                ext_time_32ms      = (`EPX16_CX_TIME_24MS*9) + `EPX16_CX_TIME_32MS; // 248ms (24ms x 10 + 8ms)
            end
            1: begin // (x2)
                ext_fast_time_24ms = fast_time_24ms*2;
                ext_fast_time_32ms = ({2'b00,fast_time_24ms} + {2'b00,fast_time_32ms}) ; // 56ms  (24ms x 2  + 8ms)
                ext_time_24ms      = (`EPX16_CX_TIME_24MS*2);
                ext_time_32ms      = `EPX16_CX_TIME_24MS     + `EPX16_CX_TIME_32MS; // 56ms  (24ms x 2  + 8ms)
            end
            default: begin
                ext_fast_time_24ms = {2'b00,fast_time_24ms};
                ext_fast_time_32ms = {2'b00,fast_time_32ms};
                ext_time_24ms      = `EPX16_CX_TIME_24MS;
                ext_time_32ms      = `EPX16_CX_TIME_32MS;
            end
        endcase


endmodule
