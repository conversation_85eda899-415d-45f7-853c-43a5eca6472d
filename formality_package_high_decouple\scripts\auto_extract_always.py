# -*- coding: utf-8 -*-
"""
自动化批量提取高解耦度always块为submodule的脚本
基于analyze_always_blocks.py的分析结果，自动调用gen_always_to_submodule.py
作者：AI助手
"""
import os
import sys
import subprocess
import argparse
from collections import defaultdict
import re

def read_file_lines(filename):
    """读取文件，尝试多种编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as f:
                return f.readlines()
        except UnicodeDecodeError:
            continue
    
    # 如果所有编码都失败，使用utf-8并忽略错误
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        return f.readlines()

def extract_always_blocks(lines):
    """提取所有always块"""
    always_blocks = []
    i = 0
    while i < len(lines):
        line = lines[i]
        if re.match(r'^\s*always\b', line) and not re.match(r'^\s*//', line):
            start_line = i + 1
            if 'begin' in line:
                # begin-end块
                begin_count = line.count('begin')
                end_count = line.count('end')
                j = i + 1
                while j < len(lines) and begin_count > end_count:
                    begin_count += lines[j].count('begin')
                    end_count += lines[j].count('end')
                    j += 1
                end_line = j
            else:
                # 单行always，找到分号结束
                j = i
                while j < len(lines) and ';' not in lines[j]:
                    j += 1
                end_line = j + 1
            
            block_lines = lines[i:end_line]
            always_blocks.append((start_line, end_line, block_lines))
            i = end_line
        else:
            i += 1
    return always_blocks

def extract_written_signals(block_lines):
    """提取写入信号"""
    text = ''.join(block_lines)
    lefts = re.findall(r'([a-zA-Z_][\w\[\]]*)\s*(<=|=)', text)
    outputs = set([l[0].split('[')[0] for l in lefts])
    return outputs

def extract_read_signals(block_lines):
    """提取依赖信号"""
    text = ''.join(block_lines)
    rights = re.findall(r'(?:<=|=)\s*([^;]+);', text)
    inputs = set()
    for expr in rights:
        tokens = re.findall(r'\b([a-zA-Z_][\w]*)\b', expr)
        for t in tokens:
            if not re.match(r'^(begin|if|else|case|end|for|while|posedge|negedge|or|and|not|xor|xnor|assign|wire|reg|input|output|parameter|localparam|\d+)$', t):
                inputs.add(t)
    
    conds = re.findall(r'if\s*\(([^\)]+)\)', text)
    for cond in conds:
        tokens = re.findall(r'\b([a-zA-Z_][\w]*)\b', cond)
        for t in tokens:
            if not re.match(r'^(begin|if|else|case|end|for|while|posedge|negedge|or|and|not|xor|xnor|assign|wire|reg|input|output|parameter|localparam|\d+)$', t):
                inputs.add(t)
    return inputs

def analyze_always_blocks(filename):
    """分析always块，返回高解耦度的块信息"""
    lines = read_file_lines(filename)
    always_blocks = extract_always_blocks(lines)
    
    # 统计信号写入次数
    signal_write_count = defaultdict(int)
    block_info = []
    
    for start, end, block in always_blocks:
        writes = extract_written_signals(block)
        reads = extract_read_signals(block)
        
        for w in writes:
            signal_write_count[w] += 1
        
        block_info.append({
            'start': start,
            'end': end,
            'writes': writes,
            'reads': reads,
            'block': block
        })
    
    # 筛选高解耦度的always块
    high_decouple_blocks = []
    for i, info in enumerate(block_info):
        writes = info['writes']
        if writes:  # 有写入信号
            single_driver = all(signal_write_count[w] == 1 for w in writes)
            if single_driver:
                # 生成子模块名
                if len(writes) == 1:
                    submod_name = list(writes)[0] + '_sub'
                else:
                    submod_name = f'always_block_{i+1}_sub'
                
                high_decouple_blocks.append({
                    'index': i + 1,
                    'start': info['start'],
                    'end': info['end'],
                    'writes': writes,
                    'reads': reads,
                    'submod_name': submod_name
                })
    
    return high_decouple_blocks

def run_gen_submodule(filename, start, end, submod_name, gen_script_path):
    """调用gen_always_to_submodule.py生成子模块"""
    cmd = [
        'python', gen_script_path,
        filename, str(start), str(end), submod_name
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=False)
        if result.returncode == 0:
            print(f"✓ 成功生成子模块: {submod_name}")
            return True
        else:
            print(f"✗ 生成子模块失败: {submod_name}")
            # 尝试解码错误信息
            try:
                stderr_msg = result.stderr.decode('gbk', errors='ignore')
            except:
                stderr_msg = str(result.stderr)
            print(f"错误信息: {stderr_msg}")
            return False
    except Exception as e:
        print(f"✗ 运行gen_always_to_submodule.py时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='自动化批量提取高解耦度always块为submodule')
    parser.add_argument('filename', help='要分析的sv/v文件')
    parser.add_argument('--output-dir', default='./submodules', help='子模块输出目录')
    args = parser.parse_args()
    
    # 检查gen_always_to_submodule.py是否存在
    if not os.path.exists('gen_always_to_submodule.py'):
        print("错误: 找不到gen_always_to_submodule.py文件")
        sys.exit(1)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"正在分析文件: {args.filename}")
    print("="*60)
    
    # 分析always块
    high_blocks = analyze_always_blocks(args.filename)
    
    if not high_blocks:
        print("未找到可安全解耦的always块")
        return
    
    print(f"找到 {len(high_blocks)} 个高解耦度always块:")
    for block in high_blocks:
        print(f"  Always块 #{block['index']} (行号: {block['start']}-{block['end']}) -> {block['submod_name']}")
    
    print("\n开始批量生成子模块...")
    print("="*60)
    
    success_count = 0
    total_count = len(high_blocks)
    
    # 切换到输出目录
    original_dir = os.getcwd()
    os.chdir(args.output_dir)
    
    try:
        for block in high_blocks:
            print(f"\n处理 Always块 #{block['index']} (行号: {block['start']}-{block['end']})")
            print(f"  写入信号: {sorted(block['writes'])}")
            print(f"  依赖信号: {sorted(block['reads'])}")
            
            # 调用gen_always_to_submodule.py
            filename_path = os.path.join(original_dir, args.filename)
            if run_gen_submodule(filename_path, block['start'], block['end'], block['submod_name'], '../gen_always_to_submodule.py'):
                success_count += 1
                
                # 重命名生成的文件
                if os.path.exists('instantiation.txt'):
                    new_inst_name = f"{block['submod_name']}_instantiation.txt"
                    os.rename('instantiation.txt', new_inst_name)
                    print(f"  生成文件: {block['submod_name']}.v")
                    print(f"  生成文件: {new_inst_name}")
    
    finally:
        # 恢复原目录
        os.chdir(original_dir)
    
    print("\n" + "="*60)
    print(f"批量处理完成: {success_count}/{total_count} 个always块成功生成子模块")
    print(f"输出目录: {args.output_dir}")

if __name__ == '__main__':
    main()






