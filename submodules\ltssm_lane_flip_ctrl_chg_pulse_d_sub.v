module ltssm_lane_flip_ctrl_chg_pulse_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire int_smlh_lane_flip_ctrl,
    input wire ltssm_mid_config_state,
    input wire next_smlh_lane_flip_ctrl,
    output reg [N-1:0] ltssm_lane_flip_ctrl_chg_pulse_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : ltssm_lane_flip_ctrl_chg_pulse_d_PROC
        if (!core_rst_n)
            ltssm_lane_flip_ctrl_chg_pulse_d <= #TP 0; //reg to break Combinatorial Loop
        else
            ltssm_lane_flip_ctrl_chg_pulse_d <= #TP ltssm_mid_config_state & (int_smlh_lane_flip_ctrl != next_smlh_lane_flip_ctrl);
    end // ltssm_lane_flip_ctrl_chg_pulse_d_PROC


endmodule
