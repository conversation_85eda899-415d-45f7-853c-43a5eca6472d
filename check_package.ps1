﻿# 验证包完整性检查脚本
# 检查formality验证包是否包含所有必要文件

Write-Host "=== Formality验证包完整性检查 ===" -ForegroundColor Green

# 检查目录结构
 = @("original", "replaced", "submodules", "scripts")
foreach ( in ) {
    if (Test-Path "formality_package\") {
        Write-Host "✓ 目录  存在" -ForegroundColor Green
    } else {
        Write-Host "✗ 目录  缺失" -ForegroundColor Red
    }
}

# 检查关键文件
Write-Host "
=== 关键文件检查 ===" -ForegroundColor Yellow
 = @(
    "formality_package\original\EPX16_smlh_ltssm-7854.sv",
    "formality_package\replaced\EPX16_smlh_ltssm-7854_replaced.sv", 
    "formality_package\run_formality.tcl",
    "formality_package\README.md"
)

foreach ( in ) {
    if (Test-Path ) {
         = (Get-Item ).Length
        Write-Host "✓  ( bytes)" -ForegroundColor Green
    } else {
        Write-Host "✗  缺失" -ForegroundColor Red
    }
}

# 统计子模块数量
 = (Get-ChildItem "formality_package\submodules\*.v").Count
Write-Host "
=== 子模块统计 ===" -ForegroundColor Yellow
Write-Host "子模块数量: " -ForegroundColor Cyan

# 检查脚本文件
 = (Get-ChildItem "formality_package\scripts\*.py").Count
Write-Host "Python脚本数量: " -ForegroundColor Cyan

Write-Host "
=== 验证包准备完成 ===" -ForegroundColor Green
Write-Host "可以使用以下命令运行formality验证:" -ForegroundColor White
Write-Host "cd formality_package" -ForegroundColor Cyan
Write-Host "formality -f run_formality.tcl" -ForegroundColor Cyan
