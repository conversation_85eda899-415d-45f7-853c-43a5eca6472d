module rcvd_eidle_cnt_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire b1,
    input wire clear,
    input wire eidle_cnt_clear,
    input wire rcvd_valid_eidle_set,
    output reg [N-1:0] rcvd_eidle_cnt // TODO: 请根据实际宽度修改
);

    always@(posedge core_clk or negedge core_rst_n) begin : EIDLE_CNT
      if (!core_rst_n) begin
        rcvd_eidle_cnt <= #TP 0;
      end else begin
          if (clear || eidle_cnt_clear[0])
            rcvd_eidle_cnt[3:0] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[3:0]>=4)
            rcvd_eidle_cnt[3:0] <= #TP rcvd_eidle_cnt[3:0];
          else if (rcvd_valid_eidle_set[0])
            rcvd_eidle_cnt[3:0] <= #TP rcvd_eidle_cnt[3:0] + 1'b1;

          if (clear || eidle_cnt_clear[1])
            rcvd_eidle_cnt[7:4] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[7:4]>=4)
            rcvd_eidle_cnt[7:4] <= #TP rcvd_eidle_cnt[7:4];
          else if (rcvd_valid_eidle_set[1])
            rcvd_eidle_cnt[7:4] <= #TP rcvd_eidle_cnt[7:4] + 1'b1;

          if (clear || eidle_cnt_clear[2])
            rcvd_eidle_cnt[11:8] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[11:8]>=4)
            rcvd_eidle_cnt[11:8] <= #TP rcvd_eidle_cnt[11:8];
          else if (rcvd_valid_eidle_set[2])
            rcvd_eidle_cnt[11:8] <= #TP rcvd_eidle_cnt[11:8] + 1'b1;

          if (clear || eidle_cnt_clear[3])
            rcvd_eidle_cnt[15:12] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[15:12]>=4)
            rcvd_eidle_cnt[15:12] <= #TP rcvd_eidle_cnt[15:12];
          else if (rcvd_valid_eidle_set[3])
            rcvd_eidle_cnt[15:12] <= #TP rcvd_eidle_cnt[15:12] + 1'b1;

          if (clear || eidle_cnt_clear[4])
            rcvd_eidle_cnt[19:16] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[19:16]>=4)
            rcvd_eidle_cnt[19:16] <= #TP rcvd_eidle_cnt[19:16];
          else if (rcvd_valid_eidle_set[4])
            rcvd_eidle_cnt[19:16] <= #TP rcvd_eidle_cnt[19:16] + 1'b1;

          if (clear || eidle_cnt_clear[5])
            rcvd_eidle_cnt[23:20] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[23:20]>=4)
            rcvd_eidle_cnt[23:20] <= #TP rcvd_eidle_cnt[23:20];
          else if (rcvd_valid_eidle_set[5])
            rcvd_eidle_cnt[23:20] <= #TP rcvd_eidle_cnt[23:20] + 1'b1;

          if (clear || eidle_cnt_clear[6])
            rcvd_eidle_cnt[27:24] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[27:24]>=4)
            rcvd_eidle_cnt[27:24] <= #TP rcvd_eidle_cnt[27:24];
          else if (rcvd_valid_eidle_set[6])
            rcvd_eidle_cnt[27:24] <= #TP rcvd_eidle_cnt[27:24] + 1'b1;

          if (clear || eidle_cnt_clear[7])
            rcvd_eidle_cnt[31:28] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[31:28]>=4)
            rcvd_eidle_cnt[31:28] <= #TP rcvd_eidle_cnt[31:28];
          else if (rcvd_valid_eidle_set[7])
            rcvd_eidle_cnt[31:28] <= #TP rcvd_eidle_cnt[31:28] + 1'b1;

          if (clear || eidle_cnt_clear[8])
            rcvd_eidle_cnt[35:32] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[35:32]>=4)
            rcvd_eidle_cnt[35:32] <= #TP rcvd_eidle_cnt[35:32];
          else if (rcvd_valid_eidle_set[8])
            rcvd_eidle_cnt[35:32] <= #TP rcvd_eidle_cnt[35:32] + 1'b1;

          if (clear || eidle_cnt_clear[9])
            rcvd_eidle_cnt[39:36] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[39:36]>=4)
            rcvd_eidle_cnt[39:36] <= #TP rcvd_eidle_cnt[39:36];
          else if (rcvd_valid_eidle_set[9])
            rcvd_eidle_cnt[39:36] <= #TP rcvd_eidle_cnt[39:36] + 1'b1;

          if (clear || eidle_cnt_clear[10])
            rcvd_eidle_cnt[43:40] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[43:40]>=4)
            rcvd_eidle_cnt[43:40] <= #TP rcvd_eidle_cnt[43:40];
          else if (rcvd_valid_eidle_set[10])
            rcvd_eidle_cnt[43:40] <= #TP rcvd_eidle_cnt[43:40] + 1'b1;

          if (clear || eidle_cnt_clear[11])
            rcvd_eidle_cnt[47:44] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[47:44]>=4)
            rcvd_eidle_cnt[47:44] <= #TP rcvd_eidle_cnt[47:44];
          else if (rcvd_valid_eidle_set[11])
            rcvd_eidle_cnt[47:44] <= #TP rcvd_eidle_cnt[47:44] + 1'b1;

          if (clear || eidle_cnt_clear[12])
            rcvd_eidle_cnt[51:48] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[51:48]>=4)
            rcvd_eidle_cnt[51:48] <= #TP rcvd_eidle_cnt[51:48];
          else if (rcvd_valid_eidle_set[12])
            rcvd_eidle_cnt[51:48] <= #TP rcvd_eidle_cnt[51:48] + 1'b1;

          if (clear || eidle_cnt_clear[13])
            rcvd_eidle_cnt[55:52] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[55:52]>=4)
            rcvd_eidle_cnt[55:52] <= #TP rcvd_eidle_cnt[55:52];
          else if (rcvd_valid_eidle_set[13])
            rcvd_eidle_cnt[55:52] <= #TP rcvd_eidle_cnt[55:52] + 1'b1;

          if (clear || eidle_cnt_clear[14])
            rcvd_eidle_cnt[59:56] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[59:56]>=4)
            rcvd_eidle_cnt[59:56] <= #TP rcvd_eidle_cnt[59:56];
          else if (rcvd_valid_eidle_set[14])
            rcvd_eidle_cnt[59:56] <= #TP rcvd_eidle_cnt[59:56] + 1'b1;

          if (clear || eidle_cnt_clear[15])
            rcvd_eidle_cnt[63:60] <= #TP 4'b0;
          else if (rcvd_eidle_cnt[63:60]>=4)
            rcvd_eidle_cnt[63:60] <= #TP rcvd_eidle_cnt[63:60];
          else if (rcvd_valid_eidle_set[15])
            rcvd_eidle_cnt[63:60] <= #TP rcvd_eidle_cnt[63:60] + 1'b1;
      end
    end


endmodule
