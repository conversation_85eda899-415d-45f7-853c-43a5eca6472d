module always_block_114_sub (
    input wire link_latched_ts_data_rate,
    input wire link_latched_ts_spd_chg,
    output reg [N-1:0] captured_ts_data_rate // TODO: 请根据实际宽度修改,
    output reg [N-1:0] captured_ts_speed_change // TODO: 请根据实际宽度修改
);

    always @( * ) begin : captured_ts_speed_change_data_rate_PROC
        captured_ts_speed_change = link_latched_ts_spd_chg;
        captured_ts_data_rate    = link_latched_ts_data_rate;
    end // captured_ts_speed_change_data_rate_PROC


endmodule
