module latched_xmtbyte_eies_sent_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear,
    input wire xmtbyte_eies_sent,
    output reg [N-1:0] latched_xmtbyte_eies_sent // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : latched_xmtbyte_eies_sent_PROC
        if ( ~core_rst_n ) begin
            latched_xmtbyte_eies_sent <= #TP 0;
        end else if ( clear ) begin
            latched_xmtbyte_eies_sent <= #TP 0;
        end else if ( xmtbyte_eies_sent ) begin
            latched_xmtbyte_eies_sent <= #TP 1;
        end
    end // latched_xmtbyte_eies_sent_PROC


endmodule
