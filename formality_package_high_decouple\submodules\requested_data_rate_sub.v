module requested_data_rate_sub (
    input wire b00001,
    input wire b00011,
    input wire b00111,
    input wire b01111,
    input wire b11111,
    output reg [N-1:0] requested_data_rate // TODO: 请根据实际宽度修改
);

    always @(*) begin
        case(cfg_target_link_speed)
            `EPX16_GEN5_LINK_SP: requested_data_rate = 5'b11111;
            `EPX16_GEN4_LINK_SP: requested_data_rate = 5'b01111;
            `EPX16_GEN3_LINK_SP: requested_data_rate = 5'b00111;
            `EPX16_GEN2_LINK_SP: requested_data_rate = 5'b00011;
            default      : requested_data_rate = 5'b00001;
        endcase


endmodule
