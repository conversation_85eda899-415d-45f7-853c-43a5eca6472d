module smlh_link_rxmode_sub (
    input wire core_rst_n,
    input wire S_CFG_LINKWD_START,
    input wire TP,
    input wire ltssm,
    input wire smlh_link_mode,
    output reg [N-1:0] smlh_link_rxmode // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : smlh_link_rxmode_PROC
        if ( ~core_rst_n )
            smlh_link_rxmode <= #TP 0;
        else if ( ltssm != S_CFG_LINKWD_START )
            smlh_link_rxmode <= #TP smlh_link_mode;
    end


endmodule
