
# -*- coding: utf-8 -*-
"""
Verilog/SystemVerilog always块独立性分析脚本
自动找出独立、安全可解耦的always块
作者：AI助手
"""
import re
import sys
import argparse
from collections import defaultdict

def read_file_lines(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return f.readlines()

def extract_always_blocks(lines):
    """
    更精确地分割所有always块，支持begin-end嵌套和单行always
    返回[(start_line, end_line, block_lines)]
    """
    blocks = []
    idx = 0
    n = len(lines)
    while idx < n:
        line = lines[idx]
        # 只匹配非注释行的always
        if re.match(r'^\s*always\b', line) and not re.match(r'^\s*//', line):
            block_start = idx
            block_lines = [line]
            # 检查本行是否有begin
            has_begin = 'begin' in line
            begin_count = line.count('begin')
            end_count = line.count('end')
            if has_begin or (idx + 1 < n and 'begin' in lines[idx + 1]):
                # 进入begin-end计数模式
                # 如果本行没有begin，检查下一行
                if not has_begin and idx + 1 < n:
                    idx += 1
                    l = lines[idx]
                    block_lines.append(l)
                    begin_count += l.count('begin')
                    end_count += l.count('end')
                while begin_count > end_count and idx + 1 < n:
                    idx += 1
                    l = lines[idx]
                    block_lines.append(l)
                    begin_count += l.count('begin')
                    end_count += l.count('end')
                block_end = idx
            else:
                # 没有begin，遇到第一个分号即为结束
                while ';' not in line and idx + 1 < n:
                    idx += 1
                    line = lines[idx]
                    block_lines.append(line)
                block_end = idx
            blocks.append((block_start + 1, block_end + 1, block_lines))
        idx += 1
    return blocks

def extract_written_signals(block_lines):
    """
    提取always块内所有被赋值的信号（左值）
    """
    text = ''.join(block_lines)
    # 匹配 reg <=, reg =, reg[xx] <=, reg[xx] =
    lefts = re.findall(r'([a-zA-Z_][\w\[\]]*)\s*(<=|=)', text)
    # 只取信号名部分
    return set([l[0].split('[')[0] for l in lefts])

def extract_read_signals(block_lines):
    """
    提取always块内所有被用作右值的信号
    """
    text = ''.join(block_lines)
    # 匹配所有赋值语句右侧
    rights = re.findall(r'(?:<=|=)\s*([^;]+);', text)
    signals = set()
    for expr in rights:
        # 分词，去除数字、常量、运算符
        tokens = re.findall(r'\b([a-zA-Z_][\w]*)\b', expr)
        for t in tokens:
            if not re.match(r'^(begin|if|else|case|end|for|while|always|posedge|negedge|or|and|not|xor|xnor|assign|wire|reg|input|output|parameter|localparam)$', t):
                signals.add(t)
    return signals

def main():
    # 保存原始stdout
    original_stdout = sys.stdout
    # 打开log.txt用于写入
    log_file = open('log.txt', 'w', encoding='utf-8')
    # 定义一个类，实现同时写入文件和终端
    class Tee(object):
        def __init__(self, *files):
            self.files = files
        def write(self, obj):
            for f in self.files:
                f.write(obj)
                f.flush()
        def flush(self):
            for f in self.files:
                f.flush()
    # 重定向sys.stdout
    sys.stdout = Tee(original_stdout, log_file)

    # parser = argparse.ArgumentParser(description='分析Verilog/SystemVerilog文件，找出独立、安全可解耦的always块')
    # parser.add_argument('verilog-example/EPX16_smlh_ltssm-7854.sv', help='要分析的sv/v文件')
    # args = parser.parse_args()

    # lines = read_file_lines(args.filename)
    lines = read_file_lines('verilog-example/EPX16_smlh_ltssm-7854.sv')
    always_blocks = extract_always_blocks(lines)

    # 统计所有信号被写入的次数
    signal_write_count = defaultdict(int)
    block_write_signals = []
    block_read_signals = []
    for start, end, block in always_blocks:
        writes = extract_written_signals(block)
        reads = extract_read_signals(block)
        block_write_signals.append(writes)
        block_read_signals.append(reads)
        for w in writes:
            signal_write_count[w] += 1

    print(f'共找到 {len(always_blocks)} 个always块')
    print('='*60)
    for idx, (start, end, block) in enumerate(always_blocks):
        writes = block_write_signals[idx]
        reads = block_read_signals[idx]
        # 判断是否所有写入信号都是单驱动
        single_driver = all(signal_write_count[w] == 1 for w in writes) if writes else False
        # 推荐解耦等级
        if single_driver and writes:
            level = '高（可安全解耦）'
        elif writes:
            level = '中（部分信号多驱动，需人工复核）'
        else:
            level = '低（无寄存器写入，或仅组合逻辑）'
        print(f'Always块 #{idx+1}  行号: {start}-{end}')
        print(f'  写入信号: {sorted(writes) if writes else "无"}')
        print(f'  依赖信号: {sorted(reads) if reads else "无"}')
        print(f'  所有写入信号单驱动: {single_driver}')
        print(f'  推荐解耦等级: {level}')
        print('-'*60)

    # 恢复stdout，关闭log文件
    sys.stdout = original_stdout
    log_file.close()

if __name__ == '__main__':
    main() 