# Formality等价验证脚本
# 自动生成于Verilog重构项目

# 设置工作目录
set_app_var hdlin_enable_presto_for_verilog true
set_app_var verification_verify_directly_undriven_output true

# 读取原始设计（参考设计）
read_verilog -r {./original/EPX16_smlh_ltssm-7854.sv}
set_top r:WORK/EPX16_smlh_ltssm

# 读取重构后设计（实现设计）
read_verilog -i {./replaced/EPX16_smlh_ltssm-7854_replaced.sv}

# 读取所有子模块
read_verilog -i [glob ./submodules/*.v]
read_verilog -i [glob ./submodules/*.sv]

set_top i:WORK/EPX16_smlh_ltssm

# 匹配设计
match

# 验证等价性
verify

# 生成报告
report_failing_points > failing_points.rpt
report_verification > verification.rpt

# 如果验证失败，生成调试信息
if {[get_verification_status] != "SUCCESSFUL"} {
    report_aborted > aborted.rpt
    report_unverified > unverified.rpt
    save_session -replace formality_debug.fss
    puts "验证失败，请检查报告文件"
} else {
    puts "等价验证成功！"
}

exit
