module mod_ts_for_ts2_lid_deskew_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire mod_ts_for_ts2_lid_deskew,
    output reg [N-1:0] mod_ts_for_ts2_lid_deskew_d // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : mod_ts_for_ts2_lid_deskew_d_PROC
        if ( ~core_rst_n )
            mod_ts_for_ts2_lid_deskew_d <= #TP 0;
        else
            mod_ts_for_ts2_lid_deskew_d <= #TP mod_ts_for_ts2_lid_deskew;
    end // mod_ts_for_ts2_lid_deskew_d_PROC


endmodule
