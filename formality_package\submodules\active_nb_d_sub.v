module active_nb_d_sub (
    input wire core_rst_n,
    input wire EPX16_CX_NB,
    input wire TP,
    input wire active_nb,
    output reg [N-1:0] active_nb_d // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : active_nb_d_PROC
        if ( ~core_rst_n ) begin
            active_nb_d <= #TP `EPX16_CX_NB;
        end else begin
            active_nb_d <= #TP active_nb;
        end
    end // active_nb_d_PROC


endmodule
