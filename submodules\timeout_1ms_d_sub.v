module timeout_1ms_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire timeout_1ms,
    output reg [N-1:0] timeout_1ms_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : timeout_1ms_d_PROC
        if ( ~core_rst_n ) begin
            timeout_1ms_d <= #TP 0;
        end else begin
            timeout_1ms_d <= #TP timeout_1ms;
        end
    end // timeout_1ms_d_PROC


endmodule
