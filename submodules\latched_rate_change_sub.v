module latched_rate_change_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b1,
    input wire clear,
    input wire current_data_rate,
    input wire current_data_rate_d,
    output reg [N-1:0] latched_rate_change // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : latched_rate_change_PROC
        if ( ~core_rst_n )
            latched_rate_change <= #TP 0;
        else if(clear)
            latched_rate_change <= #TP 0;
        else if(current_data_rate_d != current_data_rate)
            latched_rate_change <= #TP 1'b1;
    end //always


endmodule
