module ltssm_timer_24ms_cnt_sub (
    input wire core_rst_n,
    input wire clear,
    input wire timer_24ms_cnt_i,
    output reg [N-1:0] ltssm_timer_24ms_cnt // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : ltssm_timer_24ms_cnt_PROC
        if (!core_rst_n) begin
            ltssm_timer_24ms_cnt <= 0;
        end else if ( clear ) begin
            ltssm_timer_24ms_cnt <= 0;
        end else begin
            ltssm_timer_24ms_cnt <= timer_24ms_cnt_i;
        end
    end //always @(posedge


endmodule
