module always_block_238_sub (
    input wire b0,
    input wire bypass_g3_eq,
    input wire bypass_g4_eq,
    output reg [N-1:0] cfgcmpl_all_8_ts2_rcvd // TODO: 请根据实际宽度修改,
    output reg [N-1:0] int_bypass_gen3_eq // TODO: 请根据实际宽度修改,
    output reg [N-1:0] int_bypass_gen4_eq // TODO: 请根据实际宽度修改,
    output reg [N-1:0] int_ltssm_cmd_send_eieos_for_pset_map // TODO: 请根据实际宽度修改,
    output reg [N-1:0] int_rcvd_8_ts2_noeq_nd // TODO: 请根据实际宽度修改,
    output reg [N-1:0] int_rcvd_8_ts2_skip_eq // TODO: 请根据实际宽度修改,
    output reg [N-1:0] int_rcvd_8expect_ts1 // TODO: 请根据实际宽度修改,
    output reg [N-1:0] ltssm_in_hotrst_dis_entry // TODO: 请根据实际宽度修改
);

    always @( * ) begin : next_lts_state_PROC
            ltssm_in_hotrst_dis_entry = 1'b0;
            int_rcvd_8_ts2_skip_eq = 0;
            cfgcmpl_all_8_ts2_rcvd = 0;
            int_rcvd_8_ts2_noeq_nd = 0;
            int_bypass_gen3_eq = bypass_g3_eq;
            int_bypass_gen4_eq = bypass_g4_eq;
            int_rcvd_8expect_ts1 = 0;
            int_ltssm_cmd_send_eieos_for_pset_map = 0;


endmodule
