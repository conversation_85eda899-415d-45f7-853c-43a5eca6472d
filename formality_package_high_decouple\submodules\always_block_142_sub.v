module always_block_142_sub (
    input wire core_rst_n,
    input wire TP,
    input wire clear,
    input wire directed_speed_change,
    output reg [N-1:0] clear_d // TODO: 请根据实际宽度修改,
    output reg [N-1:0] directed_speed_change_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : directed_speed_change_d_PROC
        if ( ~core_rst_n ) begin
            directed_speed_change_d <= #TP 0;
            clear_d                 <= #TP 0;
        end else begin
            directed_speed_change_d <= #TP directed_speed_change;
            clear_d                 <= #TP clear;
        end
    end //directed_speed_change_d_PROC


endmodule
