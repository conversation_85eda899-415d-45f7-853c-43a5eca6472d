module mac_phy_txswing_sub (
    input wire sticky_rst_n,
    input wire EPX16_DEFAULT_GEN2_TXSWING,
    input wire S_DETECT_QUIET,
    input wire TP,
    input wire cfg_phy_txswing,
    input wire lts_state,
    output reg [N-1:0] mac_phy_txswing // TODO: 请根据实际宽度修改
);

    always@(posedge core_clk or negedge sticky_rst_n) begin : mac_phy_txswing_PROC
        if (!sticky_rst_n)
            mac_phy_txswing <= #TP `EPX16_DEFAULT_GEN2_TXSWING;
        // In Detect.Quiet, mac_phy_txswing is not permitted to change.
        else if ( lts_state != S_DETECT_QUIET )
            mac_phy_txswing <= #TP cfg_phy_txswing;
    end    


endmodule
