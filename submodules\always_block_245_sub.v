module always_block_245_sub (
    input wire core_rst_n,
    input wire Entry,
    input wire Lpbk,
    input wire TP,
    input wire active,
    input wire b0,
    input wire b1,
    input wire clear,
    input wire in,
    input wire is,
    input wire link_any_2_ts1_lpbk1_rcvd,
    input wire means,
    input wire perform_eq_for_lpbk_mstr,
    input wire slave,
    output reg [N-1:0] lpbk // TODO: 请根据实际宽度修改,
    output reg [N-1:0] lpbk_master_eq_exit // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : lpbk_master_eq_exit_PROC
        if ( ~core_rst_n ) begin
            lpbk_master_eq_exit <= #TP 1'b0;
        end else if ( clear ) begin // clear when state transitions
            lpbk_master_eq_exit <= #TP 1'b0;
        end else if ( perform_eq_for_lpbk_mstr && link_any_2_ts1_lpbk1_rcvd ) begin // when master receives 2 ts1s with lpbk=1 in Lpbk.Entry, means slave is in lpbk.active
            lpbk_master_eq_exit <= #TP 1'b1;
        end
    end // lpbk_master_eq_exit_PROC


endmodule
