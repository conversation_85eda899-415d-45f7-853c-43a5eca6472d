module directed_link_width_change_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire directed_link_width_change,
    output reg [N-1:0] directed_link_width_change_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : directed_link_width_change_d_PROC
        if ( ~core_rst_n ) begin
            directed_link_width_change_d <= #TP 0;
        end else begin
            directed_link_width_change_d <= #TP directed_link_width_change;
        end
    end


endmodule
