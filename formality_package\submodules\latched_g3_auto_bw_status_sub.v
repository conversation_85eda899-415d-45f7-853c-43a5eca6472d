module latched_g3_auto_bw_status_sub (
    input wire core_rst_n,
    input wire TP,
    input wire auto_eq_phase_flag_ff,
    input wire b1,
    input wire g3_rate,
    input wire smlh_link_auto_bw_status,
    input wire soft_eq_phase_flag_ff,
    output reg [N-1:0] latched_g3_auto_bw_status // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : latched_g3_auto_bw_status_PROC
        if ( ~core_rst_n )
            latched_g3_auto_bw_status <= #TP 0;
        else if(auto_eq_phase_flag_ff || soft_eq_phase_flag_ff) begin
            if( smlh_link_auto_bw_status && g3_rate)
                latched_g3_auto_bw_status <= #TP 1'b1;
        end
        else
            latched_g3_auto_bw_status <= #TP 0;
    end


endmodule
