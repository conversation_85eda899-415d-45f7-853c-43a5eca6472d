module latchd_smlh_lanes_rcving_sub (
    input wire core_rst_n,
    input wire TP,
    input wire int_rxdetect_done,
    input wire int_rxdetected,
    input wire ltssm_lanes_active,
    input wire smlh_lanes_rcving,
    output reg [N-1:0] latchd_smlh_lanes_rcving // TODO: 请根据实际宽度修改
);

    always@(posedge core_clk or negedge core_rst_n) begin : latchd_smlh_lanes_rcving_PROC
        if (!core_rst_n)
            latchd_smlh_lanes_rcving <= #TP 0;
        else if ( int_rxdetect_done )     // all lanes that detected a receiver during detect are considered active
            latchd_smlh_lanes_rcving <= #TP int_rxdetected & ltssm_lanes_active;
        else
            latchd_smlh_lanes_rcving <= #TP smlh_lanes_rcving;
    end // latchd_smlh_lanes_rcving_PROC


endmodule
