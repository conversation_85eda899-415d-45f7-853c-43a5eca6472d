module latched_smlh_link_up_sub (
    input wire sticky_rst_n,
    input wire TP,
    input wire b1,
    input wire smlh_link_up,
    output reg [N-1:0] latched_smlh_link_up // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge sticky_rst_n ) begin : latched_smlh_link_up_PROC
        if ( ~sticky_rst_n ) begin
            latched_smlh_link_up <= #TP 0;
        end else if ( smlh_link_up ) begin
            latched_smlh_link_up <= #TP 1'b1;
        end
    end // latched_smlh_link_up_PROC


endmodule
