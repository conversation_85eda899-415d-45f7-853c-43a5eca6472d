module latched_link_any_8_ts_linknmtx_lanenmtx_rcvd_sub (
    input wire core_rst_n,
    input wire TP,
    input wire b0,
    input wire b1,
    input wire clear,
    input wire link_any_8_ts_linknmtx_lanenmtx_rcvd,
    output reg [N-1:0] latched_link_any_8_ts_linknmtx_lanenmtx_rcvd // TODO: 请根据实际宽度修改
);

    always @( posedge core_clk or negedge core_rst_n ) begin : latched_link_any_8_ts_linknmtx_lanenmtx_rcvd_PROC
        if ( ~core_rst_n )
            latched_link_any_8_ts_linknmtx_lanenmtx_rcvd <= #TP 1'b0;
        else if ( clear )
            latched_link_any_8_ts_linknmtx_lanenmtx_rcvd <= #TP 1'b0;
        else if ( link_any_8_ts_linknmtx_lanenmtx_rcvd )
            latched_link_any_8_ts_linknmtx_lanenmtx_rcvd <= #TP 1'b1;
    end // latched_link_any_8_ts_linknmtx_lanenmtx_rcvd_PROC


endmodule
