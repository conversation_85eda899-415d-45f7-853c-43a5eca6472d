共找到 284 个always块
============================================================
Always块 #1  行号: 680-686
  写入信号: ['lts_state', 'ltssm_ts_alt_prot_info']
  依赖信号: ['S_CFG_COMPLETE', 'TP', 'h0', 'lts_state', 'ltssm_ts1_sym14_8', 'ltssm_ts2_sym14_8', 'ltssm_ts_alt_protocol', 'ltssm_ts_alt_protocol_i']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #2  行号: 693-693
  写入信号: ['ltssm_ap_success']
  依赖信号: ['ltssm_ap_success_i']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #3  行号: 1172-1181
  写入信号: ['deskew_complete_n_d', 'lts_state_d']
  依赖信号: ['S_L0', 'TP', 'b0', 'b1', 'deskew_complete_n_d', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #4  行号: 1228-1233
  写入信号: ['smlh_link_up_d']
  依赖信号: ['TP', 'b0', 'smlh_link_up']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #5  行号: 1235-1240
  写入信号: ['hold_current_data_rate']
  依赖信号: ['TP', 'current_data_rate']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #6  行号: 1252-1273
  写入信号: ['latched_target_link_speed', 'lts_state', 'next_lts_state', 'noeq_nd', 'skip_eq', 'target_link_speed']
  依赖信号: ['DSP', 'EPX16_GEN3_LINK_SP', 'EQ', 'Gen1', 'Gen3', 'S_DETECT_QUIET', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'TS2', 'TS2s', 'at', 'b0', 'b01', 'b1', 'b10', 'bypass', 'captured_ts_data_rate', 'cfg_bypass_eq_enable', 'cfg_gen3_eq_disable', 'cfg_no_eq_needed_enable', 'cfg_upstream_port', 'clear', 'controls', 'decides', 'dsp', 'eq', 'eq_bypass', 'eqctl_any_8eqts2_rcvd', 'from', 'gen3', 'in', 'including', 'latched_link_retrain_bit', 'latched_perform_eq', 'latched_target_link_speed', 'lts_state', 'ltssm_ts_cntrl', 'needed', 'next_lts_state', 'no', 'noeq_nd', 'noeq_nd_d', 'normal', 'only', 'rate', 'receives', 'redo', 'runs', 'skip', 'skip_eq', 'skip_eq_d', 'soe_g2', 'the', 'usp', 'with']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #7  行号: 1278-1285
  写入信号: ['lpbk_master_g5', 'lts_state']
  依赖信号: ['S_DETECT_QUIET', 'S_PRE_DETECT_QUIET', 'TP', 'lpbk_master_g5', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #8  行号: 1290-1298
  写入信号: ['entered_lpbk_from_cfg', 'last_lts_state', 'lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'S_DETECT_QUIET', 'S_LPBK_ENTRY', 'TP', 'b0', 'b1', 'entered_lpbk_from_cfg', 'last_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #9  行号: 1302-1325
  写入信号: ['lts_state', 'ltssm_deprecoding5_on', 'ltssm_deprecoding_on', 'ltssm_precode5_request', 'ltssm_precoding5_on', 'ltssm_precoding5_on_latched', 'ltssm_precoding_on', 'noeq_nd_d', 'skip_eq_d']
  依赖信号: ['EPX16_CX_GEN6_SPEED', 'EPX16_GEN5_RATE', 'EPX16_GEN6_RATE', 'Gen6', 'S_POLL_COMPLIANCE', 'TP', 'b0', 'b1', 'cfg_precode_request', 'current_data_rate', 'endif', 'entered_lpbk_from_cfg', 'eqctl_precode_request', 'goe_g5', 'ifdef', 'int_deprecoding_on', 'int_precoding_on', 'lts_state', 'noeq_nd', 'noeq_nd_d', 'on', 'precode_active', 'precoding_on', 'rate', 'skip_eq']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #10  行号: 1347-1357
  写入信号: ['ltssm_deprecoding_on', 'ltssm_precode_request', 'ltssm_precoding_on', 'ltssm_precoding_on_latched']
  依赖信号: ['ltssm_deprecoding5_on', 'ltssm_precode5_request', 'ltssm_precoding5_on', 'ltssm_precoding5_on_latched']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #11  行号: 1362-1369
  写入信号: ['lts_state', 'ltssm_mod_ts_rcvd', 'next_lts_state']
  依赖信号: ['S_CFG_LANENUM_WAIT', 'S_CFG_LINKWD_ACEPT', 'S_DETECT_QUIET', 'TP', 'b0', 'b1', 'clear', 'edge', 'falling', 'link_latched_modts_support', 'ltssm_mod_ts_rcvd', 'next_lts_state', 'smlh_link_up', 'smlh_link_up_falling_edge', 'when']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #12  行号: 1372-1380
  写入信号: ['eqctl_precode_request_d', 'link_pc_rcvd_d']
  依赖信号: ['TP', 'b0', 'eqctl_precode_request', 'link_pc_rcvd']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #13  行号: 1387-1392
  写入信号: ['precoding_lat_en']
  依赖信号: ['TP', 'b0', 'precoding_lat_en_w']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #14  行号: 1394-1404
  写入信号: ['int_deprecoding_on', 'int_precoding_on', 'link_pc_rcvd', 'lts_state', 'lts_state_d']
  依赖信号: ['Eq', 'LinkUp', 'Needed', 'No', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'TP', 'at', 'b0', 'be', 'edge', 'eq', 'eqctl_precode_request', 'eqctl_precode_request_d', 'falling', 'gen5_supported', 'int_deprecoding_on', 'int_precoding_on', 'keep', 'latched', 'link_pc_rcvd', 'link_pc_rcvd_d', 'linkup', 'lts_state', 'next', 'no', 'no_eq_needed', 'performed', 'precoding_lat_en', 'record', 'smlh_link_up_falling_edge', 'smlh_successful_spd_negotiation', 'the', 'to', 'used', 'with']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #15  行号: 1410-1418
  写入信号: ['latched_tx_modified_ts', 'lts_state', 'next_lts_state']
  依赖信号: ['S_DETECT_QUIET', 'S_POLL_ACTIVE', 'S_POLL_CONFIG', 'TP', 'b0', 'b1', 'b11', 'clear', 'latched_tx_modified_ts', 'lts_state', 'ltssm_ts_cntrl', 'smlh_link_up']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #16  行号: 1421-1428
  写入信号: ['lts_state', 'next_lts_state', 'use_modified_ts']
  依赖信号: ['S_CFG_LANENUM_WAIT', 'S_CFG_LINKWD_ACEPT', 'S_DETECT_QUIET', 'S_L0', 'S_RCVRY_LOCK', 'b0', 'b1', 'clear', 'latched_tx_modified_ts', 'link_latched_modts_support', 'lts_state', 'next_lts_state', 'use_modified_ts', 'use_modified_ts_d']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #17  行号: 1430-1435
  写入信号: ['use_modified_ts_d']
  依赖信号: ['TP', 'b0', 'use_modified_ts']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #18  行号: 1437-1444
  写入信号: ['lts_state', 'rx_use_modified_ts_d']
  依赖信号: ['S_CFG_LINKWD_ACEPT', 'S_DETECT_QUIET', 'S_L0', 'S_RCVRY_LOCK', 'TP', 'b0', 'b1', 'latched_tx_modified_ts', 'link_latched_modts_support', 'lts_state', 'rx_use_modified_ts_d']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #19  行号: 1450-1458
  写入信号: ['bypass_g3_eq', 'bypass_g4_eq']
  依赖信号: ['TP', 'b1', 'int_bypass_gen3_eq', 'int_bypass_gen4_eq']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #20  行号: 1468-1476
  写入信号: ['eidle_seen_d']
  依赖信号: ['S_COMPL_TX_COMPLIANCE', 'TP', 'eidle_seen_d']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #21  行号: 1481-1490
  写入信号: ['app_ltssm_enable_d', 'app_ltssm_enable_dd']
  依赖信号: ['TP', 'app_ltssm_enable', 'app_ltssm_enable_d', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #22  行号: 1495-1501
  写入信号: ['lts_state', 'smlh_training_rst_n']
  依赖信号: ['S_HOT_RESET_ENTRY', 'TP', 'b1', 'clear', 'lts_state', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #23  行号: 1506-1512
  写入信号: ['latchd_detected_lanes']
  依赖信号: ['TP', 'int_rxdetected', 'ltssm_lanes_active']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #24  行号: 1519-1524
  写入信号: ['all_phystatus_deasserted_d']
  依赖信号: ['TP', 'all_phystatus_deasserted']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #25  行号: 1531-1538
  写入信号: ['latchd_phystatus_fall']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #26  行号: 1542-1544
  写入信号: ['rxdetect_started']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #27  行号: 1573-1586
  写入信号: ['all_phy_mac_rxdetected_d', 'any_phy_mac_rxdetected_d', 'int_rxdetect_done_d', 'int_rxdetected_d', 'same_detected_lanes_d']
  依赖信号: ['TP', 'latchd_detected_lanes_for_compare', 'ltssm_lanes_active', 'phy_mac_rxdetect_done', 'phy_mac_rxdetected', 'rxdetect_started']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #28  行号: 1605-1607
  写入信号: ['latched_cdm_ras_des_recovery_req']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #29  行号: 1613-1618
  写入信号: ['smlh_all_lanes_rcvd_r']
  依赖信号: ['TP', 'smlh_all_lanes_rcvd_c']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #30  行号: 1632-1639
  写入信号: ['link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_d']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #31  行号: 1648-1653
  写入信号: ['int_smlh_lane_flip_ctrl']
  依赖信号: ['TP', 'next_smlh_lane_flip_ctrl']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #32  行号: 1662-1667
  写入信号: ['ltssm_lane_flip_ctrl_chg_pulse_d']
  依赖信号: ['TP', 'int_smlh_lane_flip_ctrl', 'ltssm_mid_config_state', 'next_smlh_lane_flip_ctrl']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #33  行号: 1675-1683
  写入信号: ['eq_for_lpbk', 'last_lts_state', 'lts_state']
  依赖信号: ['EQ', 'Entry', 'Loopback', 'S_LPBK_ACTIVE', 'S_LPBK_ENTRY', 'TP', 'b1', 'eq_for_lpbk', 'from', 'in', 'lts_state', 'reset', 'set', 'when']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #34  行号: 1685-1693
  写入信号: ['dir_cfg_lpbk_entry_active', 'last_lts_state', 'lts_state', 'next_lts_state']
  依赖信号: ['Detect', 'S_CFG_LINKWD_START', 'S_DETECT_QUIET', 'S_LPBK_ACTIVE', 'S_LPBK_ENTRY', 'TP', 'b1', 'clear', 'dir_cfg_lpbk_entry_active', 'goe_g5', 'in', 'last_lts_state', 'lts_state', 'reset', 'when']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #35  行号: 1706-1714
  写入信号: ['lpbk_lane_under_test', 'lts_state']
  依赖信号: ['Detect', 'Loopback', 'Quiet', 'S_DETECT_QUIET', 'TP', 'after', 'cfg_do_g5_lpbk_eq', 'cfg_lane_under_test', 'enter', 'lpbk_active_master_eq', 'lpbk_lane_under_test']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #36  行号: 1716-1726
  写入信号: ['current_data_rate', 'ecb_g345_compliance_lane_flip', 'lts_state', 'mac_phy_rate']
  依赖信号: ['Compl', 'Compliance', 'EPX16_GEN1_RATE', 'EPX16_GEN3_RATE', 'Enter', 'Gen1', 'Gen3', 'Polling', 'S_POLL_COMPLIANCE', 'So', 'TP', 'TX', 'according', 'base', 'before', 'bit', 'cfg_enter_compliance', 'change', 'clear', 'core', 'current_data_rate', 'ecb_g345_compliance_lane_flip', 'enough', 'exiting', 'has', 'in', 'int_smlh_lane_flip_ctrl', 'latched', 'latched_linkup_lane_flip_ctrl', 'mac_phy_rate', 'must', 'pattern', 'rate', 'set', 'so', 'spec', 'sticky', 'that', 'the', 'time', 'to', 'use', 'when']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #37  行号: 1734-1748
  写入信号: ['link_mode_part', 'link_next_link_mode']
  依赖信号: ['d1', 'link_lanes_rcving', 'link_mode_part', 'smlh_link_mode']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #38  行号: 1753-1802
  写入信号: ['NL', 'cfg_auto_flip_predet_lane', 'lts_state', 'ltssm_powerdown', 'next_smlh_lane_flip_ctrl', 'smlh_link_mode_part']
  依赖信号: ['EPX16_P1', 'L2NL', 'L2NLD2', 'L2NLD4', 'L2NLD8', 'NL', 'S_CFG_LANENUM_ACEPT', 'TX', 'all', 'b1', 'cfg_auto_flip_predet_lane', 'cfg_upstream_port', 'clear', 'continuously', 'int_smlh_lane_flip_ctrl', 'is', 'lane', 'link', 'link_all_2_ts1_linknmtx_lanen_rcvd_rising_edge', 'link_latched_live_all_2_ts1_linknmtx_lanen_rcvd', 'link_latched_live_all_2_ts1_linknmtx_lanenmtx_reversed_rcvd_rising_edge', 'link_latched_live_lane0_2_ts1_lanen0_rcvd', 'link_mode_changed', 'link_next_link_mode', 'lts_state', 'matching', 'monitored', 'next_smlh_lane_flip_ctrl', 'number', 'of', 'on', 'regardless', 'reversed', 'smlh_lanes_active', 'smlh_link_mode_part', 'state_cfg_lanenum_acept_to_cfg_lanenum_wait', 'state_cfg_lanenum_acept_wait', 'state_cfg_linkwd_acept_to_cfg_lanenum_wait', 'state_in_cfg_lanenum_wait', 'timeout']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #39  行号: 1807-1814
  写入信号: ['lts_state', 'ltssm_lanes_active_r']
  依赖信号: ['S_CFG_COMPLETE', 'TP', 'clear', 'latchd_smlh_lanes_rcving', 'ltssm_lanes_active_r', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #40  行号: 1816-1825
  写入信号: ['lts_state', 'ltssm_lanes_active_d']
  依赖信号: ['S_DETECT_QUIET', 'TP', 'lpbk_slave_in_entry_from_cfg_ebth1', 'ltssm_lanes_active', 'ltssm_lanes_active_d']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #41  行号: 1838-1848
  写入信号: ['Control', 'lpbk_eq_lanes_active', 'lts_state']
  依赖信号: ['Behaviour', 'Control', 'Enhanced', 'Link', 'S_DETECT_QUIET', 'TP', 'TS1s', 'eq', 'flipped_ebth1', 'flipped_lut', 'get', 'lane', 'lpbk', 'lpbk_eq_lanes_active', 'ltssm_lanes_active', 'perform_eq_for_loopback', 'receive', 'slave', 'test', 'the', 'to', 'under', 'with']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #42  行号: 1859-1869
  写入信号: ['lts_state', 'ltssm_lanes_activated_pulse', 'ltssm_lanes_active', 'mac_phy_txdetectrx_loopback', 'smlh_link_mode']
  依赖信号: ['Active', 'Cmpl', 'Lane0', 'Loopback', 'Lpbk', 'Mod', 'NL', 'Pattern', 'S_CFG_LINKWD_START', 'S_DETECT_QUIET', 'S_LPBK_ACTIVE', 'TP', 'b0', 'b1', 'clear', 'd1', 'directed_link_width_change_up', 'in', 'is', 'lane', 'lanes', 'lpbk_master', 'lts_state', 'ltssm_lanes_active', 'mac_phy_txdetectrx_loopback', 'master', 'on', 'only', 'packtes', 'perform_eq_for_loopback', 'perform_eq_for_lpbk_mstr', 'sends', 'slave', 'test', 'the', 'transmits', 'under', 'upconfigure_capable', 'with']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #43  行号: 1905-1907
  写入信号: ['deskew_lanes_active']
  依赖信号: ['NL', 'TP', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #44  行号: 1937-1939
  写入信号: ['deskew_lanes_active_change']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #45  行号: 1947-1953
  写入信号: ['active_nb_d']
  依赖信号: ['EPX16_CX_NB', 'TP', 'active_nb']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #46  行号: 1958-1960
  写入信号: ['smlh_do_deskew']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #47  行号: 1984-2004
  写入信号: ['int_smlh_link_mode', 'link_next_link_mode', 'lts_state']
  依赖信号: ['S_CFG_LINKWD_ACEPT', 'S_CFG_LINKWD_START', 'S_DETECT_QUIET', 'S_LPBK_ACTIVE', 'S_LPBK_ENTRY', 'S_POLL_ACTIVE', 'TP', 'b1', 'b11', 'cfg_upstream_port', 'directed_link_width_change_down', 'directed_link_width_change_up', 'directed_link_width_change_updown', 'hF', 'hFF', 'hFFFF', 'int_smlh_link_mode', 'latched_target_link_width', 'link_mode_activated_pulse', 'link_next_link_mode', 'lpbk_master', 'lts_state', 'ltssm_lanes_active']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #48  行号: 2006-2015
  写入信号: ['link_mode_changed', 'lts_state']
  依赖信号: ['S_CFG_LANENUM_ACEPT', 'b0', 'b1', 'int_smlh_link_mode', 'link_mode_changed', 'link_next_link_mode', 'state_cfg_lanenum_acept_wait']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #49  行号: 2017-2022
  写入信号: ['smlh_link_rxmode']
  依赖信号: ['S_CFG_LINKWD_START', 'TP', 'smlh_link_mode', 'smlh_link_rxmode']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #50  行号: 2024-2029
  写入信号: ['linkup_link_mode']
  依赖信号: ['TP', 'smlh_link_mode']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #51  行号: 2031-2041
  写入信号: ['smlh_no_turnoff_lanes']
  依赖信号: ['TP', 'h0001', 'h0003', 'h000f', 'h00ff', 'hffff', 'linkup_link_mode']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #52  行号: 2043-2048
  写入信号: ['latest_link_mode', 'next_lts_state']
  依赖信号: ['S_L0', 'TP', 'clear', 'latest_link_mode', 'next_lts_state', 'smlh_link_mode']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #53  行号: 2052-2054
  写入信号: ['retry_detect']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #54  行号: 2059-2061
  写入信号: ['cfg_force_en_d']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #55  行号: 2258-2278
  写入信号: ['ext_fast_time_24ms', 'ext_fast_time_32ms', 'ext_time_24ms', 'ext_time_32ms']
  依赖信号: ['EPX16_CX_TIME_24MS', 'EPX16_CX_TIME_32MS', 'b00', 'fast_time_24ms', 'fast_time_32ms']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #56  行号: 2281-2284
  写入信号: ['timer']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #57  行号: 2304-2313
  写入信号: ['lts_state', 'timer_40ns_4rl0s']
  依赖信号: ['S_CFG_IDLE', 'S_DISABLED_ENTRY', 'S_DISABLED_IDLE', 'S_L123_SEND_EIDLE', 'S_RCVRY_IDLE', 'TP', 'b0', 'lts_state', 'timer2', 'timer_40ns_4rl0s', 'timer_freq_multiplier']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #58  行号: 2343-2351
  写入信号: ['latched_eqctl_any_8eqts2_rcvd']
  依赖信号: ['S_RCVRY_RCVRCFG', 'TP', 'latched_eqctl_any_8eqts2_rcvd']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #59  行号: 2353-2355
  写入信号: ['speed_clear']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #60  行号: 2375-2383
  写入信号: ['latched_ts1_sent_in_rcvrylock']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #61  行号: 2385-2393
  写入信号: ['latched_any_rxelecidle_low', 'lts_state']
  依赖信号: ['S_L1_IDLE', 'S_RCVRY_LOCK', 'TP', 'latched_any_rxelecidle_low', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #62  行号: 2395-2405
  写入信号: ['timeout_common_mode']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #63  行号: 2413-2423
  写入信号: ['speed_changed_timer']
  依赖信号: ['TP', 'b0', 'b1', 'speed_changed_timer', 'timer2']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #64  行号: 2427-2437
  写入信号: ['speed_changed_timeout_800ns', 'speed_changed_timer']
  依赖信号: ['TIME_800NS', 'TP', 'b0', 'b1', 'speed_changed_timeout_800ns']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #65  行号: 2441-2443
  写入信号: ['speed_timer']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #66  行号: 2455-2457
  写入信号: ['speed_timeout_800ns']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #67  行号: 2463-2465
  写入信号: ['speed_timeout_6us']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #68  行号: 2471-2473
  写入信号: ['speed_timeout_1ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #69  行号: 2487-2489
  写入信号: ['latched_eidle_seen']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #70  行号: 2495-2497
  写入信号: ['latched_eidle_inferred']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #71  行号: 2507-2509
  写入信号: ['timeout_polling_eidle']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #72  行号: 2546-2555
  写入信号: ['p1_entry_factor']
  依赖信号: 无
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #73  行号: 2559-2561
  写入信号: ['timeout_40ns']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #74  行号: 2567-2576
  写入信号: ['lts_state', 'timeout_40ns_4rl0s']
  依赖信号: ['EPX16_CX_TIME_40NS', 'S_CFG_IDLE', 'S_DISABLED_ENTRY', 'S_DISABLED_IDLE', 'S_L123_SEND_EIDLE', 'S_RCVRY_IDLE', 'TP', 'b1', 'lts_state', 'nfts_factor', 'p1_entry_factor', 'rasdes_interval_factor', 'timeout_40ns_4rl0s']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #75  行号: 2578-2580
  写入信号: ['timeout_1ms']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #76  行号: 2586-2592
  写入信号: ['timeout_1ms_d']
  依赖信号: ['TP', 'timeout_1ms']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #77  行号: 2596-2598
  写入信号: ['timeout_1us']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #78  行号: 2604-2606
  写入信号: ['timeout_10us']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #79  行号: 2614-2616
  写入信号: ['timeout_2ms']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #80  行号: 2622-2627
  写入信号: ['timeout_2ms_d']
  依赖信号: ['TP', 'timeout_2ms']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #81  行号: 2631-2636
  写入信号: ['timeout_12ms_d']
  依赖信号: ['TP', 'timeout_12ms']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #82  行号: 2640-2645
  写入信号: ['timeout_24ms_d']
  依赖信号: ['TP', 'timeout_24ms']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #83  行号: 2649-2656
  写入信号: ['timeout_3ms']
  依赖信号: ['TP', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #84  行号: 2658-2660
  写入信号: ['timeout_12ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #85  行号: 2666-2668
  写入信号: ['timeout_10ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #86  行号: 2674-2676
  写入信号: ['timeout_24ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #87  行号: 2691-2704
  写入信号: ['cnt_i', 'timer_24ms_cnt_i']
  依赖信号: ['EPX16_ZBITS', 'cfg_fast_link_mode', 'cnt_i', 'compare_t_wd_pz', 'fast_time_1ms', 'ltssm_timer_24ms_cnt', 'timer', 'timer_24ms_cnt_i']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #88  行号: 2706-2714
  写入信号: ['ltssm_timer_24ms_cnt']
  依赖信号: ['timer_24ms_cnt_i']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #89  行号: 2716-2718
  写入信号: ['timeout_32ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #90  行号: 2730-2732
  写入信号: ['timeout_48ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #91  行号: 2740-2742
  写入信号: ['timeout_100ms']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #92  行号: 2748-2750
  写入信号: ['timeout_nfts']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #93  行号: 2803-2813
  写入信号: ['i', 'latchd_rxeidle_exit']
  依赖信号: ['TP', 'b1', 'i', 'latchd_rxeidle_exit', 'ltssm_lanes_active', 'phy_mac_rxelecidle', 'predet_lanes']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #94  行号: 2826-2828
  写入信号: ['phy_mac_rxelecidle_d']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #95  行号: 2834-2846
  写入信号: ['i', 'latched_rxeidle_exit_detected']
  依赖信号: ['S_POLL_ACTIVE', 'TP', 'i', 'latched_rxeidle_exit_detected', 'rxelecidle_fall']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #96  行号: 2848-2862
  写入信号: ['i', 'latched_rxeidle']
  依赖信号: ['S_POLL_ACTIVE', 'TP', 'i', 'latched_rxeidle', 'latched_rxeidle_exit_detected']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #97  行号: 2869-2871
  写入信号: ['latched_enter_compliance']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #98  行号: 2898-2905
  写入信号: ['latchd_smlh_lanes_rcving']
  依赖信号: ['TP', 'int_rxdetected', 'ltssm_lanes_active', 'smlh_lanes_rcving']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #99  行号: 2911-2921
  写入信号: ['lts_state', 'rcvd_ts_auto_change']
  依赖信号: ['S_CFG_LANENUM_ACEPT', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'TP', 'b0', 'b1', 'link_any_8_ts_auto_chg', 'link_ln0_2_ts1_linknmtx_lanenmtx_auto_chg_rcvd', 'rcvd_ts_auto_change']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #100  行号: 2925-2927
  写入信号: ['ts_sent_in_poll_active']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #101  行号: 2936-2945
  写入信号: ['latched_ts1_sent', 'lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'TP', 'b0', 'b1', 'clear', 'in', 'latch', 'latched_ts1_sent', 'xmtbyte_ts1_sent']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #102  行号: 2948-2956
  写入信号: ['rcvd_8idles']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #103  行号: 2958-2966
  写入信号: ['rcvd_1idle']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #104  行号: 2968-2971
  写入信号: ['idle_sent_cnt']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #105  行号: 2992-2994
  写入信号: ['ts_to_poll_cmp_d']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #106  行号: 3000-3002
  写入信号: ['send_mod_compliance']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #107  行号: 3010-3012
  写入信号: ['latched_ts_deemphasis']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #108  行号: 3021-3023
  写入信号: ['latched_ts_deemphasis_var']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #109  行号: 3034-3041
  写入信号: ['latched_link_any_8_ts_linknmtx_lanenmtx_rcvd']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #110  行号: 3045-3047
  写入信号: ['latched_cmp_data_rate']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #111  行号: 3055-3057
  写入信号: ['latched_ts_nfts']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #112  行号: 3063-3065
  写入信号: ['latched_ts_data_rate']
  依赖信号: ['link_latched_ts_data_rate']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #113  行号: 3068-3070
  写入信号: ['latched_ts_data_rate_ever']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #114  行号: 3079-3082
  写入信号: ['captured_ts_data_rate', 'captured_ts_speed_change']
  依赖信号: ['link_latched_ts_data_rate', 'link_latched_ts_spd_chg']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #115  行号: 3090-3094
  写入信号: ['latched_ts_lpbk_data_rate', 'latched_ts_lpbk_deemphasis']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #116  行号: 3104-3111
  写入信号: ['requested_data_rate']
  依赖信号: ['b00001', 'b00011', 'b00111', 'b01111', 'b11111']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #117  行号: 3118-3123
  写入信号: ['int_latched_lpbk_ts_data_rate', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'S_LPBK_ENTRY', 'TP', 'clear', 'int_latched_lpbk_ts_data_rate', 'link_latched_lpbk_ts_data_rate', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #118  行号: 3126-3134
  写入信号: ['captured_lpbk_ts_auto_change', 'captured_lpbk_ts_data_rate', 'lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'any_2_ts1_lpbk1_ebth1_rcvd', 'captured_lpbk_ts_data_rate', 'clear', 'int_latched_lpbk_ts_data_rate', 'link_latched_live_all_2_ts1_lpbk1_rcvd', 'link_lpbk_ts_data_rate', 'link_lpbk_ts_deemphasis', 'loopback', 'ltssm_ts_data_rate', 'requested_data_rate', 'slave']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #119  行号: 3136-3138
  写入信号: ['rcvry_to_lpbk']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #120  行号: 3144-3146
  写入信号: ['lts_state_d']
  依赖信号: ['S_DETECT_QUIET', 'TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #121  行号: 3151-3157
  写入信号: ['latched_smlh_link_up']
  依赖信号: ['TP', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #122  行号: 3160-3173
  写入信号: ['latched_linkup_lane_flip_ctrl_lpbk', 'next_lts_state']
  依赖信号: ['Configuration', 'Loopback', 'S_CFG_LINKWD_START', 'S_LPBK_ACTIVE', 'TP', 'cfg_mux_lpbk_lanenum', 'clear', 'from', 'int_smlh_lane_flip_ctrl', 'latched_linkup_lane_flip_ctrl_lpbk', 'latched_smlh_link_up', 'linkup', 'master', 'next_lts_state', 'prevent', 'smlh_link_up', 'state', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #123  行号: 3176-3181
  写入信号: ['latched_et_cfg_lane_flip_ctrl_lpbk', 'lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'TP', 'int_smlh_lane_flip_ctrl', 'latched_et_cfg_lane_flip_ctrl_lpbk', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #124  行号: 3185-3191
  写入信号: ['latched_linkup_lane_flip_ctrl', 'smlh_link_up']
  依赖信号: ['Loopback', 'TP', 'implementation', 'in', 'int_smlh_lane_flip_ctrl', 'latched_linkup_lane_flip_ctrl', 'specific', 'state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #125  行号: 3195-3202
  写入信号: ['latched_flip_ctrl', 'linkup', 'lts_state']
  依赖信号: ['Active', 'Loopback', 'S_DETECT_QUIET', 'TP', 'in', 'int_smlh_lane_flip_ctrl', 'latched_flip_ctrl', 'loopback', 'master', 'state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #126  行号: 3204-3218
  写入信号: ['idle_to_rlock', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_IDLE', 'S_DETECT_QUIET', 'S_L0', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'TP', 'b1', 'cfg_gen2_support', 'clear', 'g1_rate', 'g3_rate', 'g4_rate', 'g5_rate', 'gen3', 'hff', 'idle_to_rlock', 'lts_state', 'next_lts_state', 'reset', 'rplh_pkt_start', 'timeout_2ms', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #127  行号: 3223-3279
  写入信号: ['cfg_pcie_max_link_speed', 'cfg_target_link_speed', 'curnt_compliance_state', 'lts_state', 'lts_state_d', 'next_data_rate', 'next_lts_state', 'precoding_on']
  依赖信号: ['Active', 'Change', 'Compliance', 'Detect', 'EPX16_GEN1_LINK_SP', 'EPX16_GEN1_RATE', 'EPX16_GEN2_LINK_SP', 'EPX16_GEN3_LINK_SP', 'EPX16_GEN4_LINK_SP', 'EPX16_GEN5_LINK_SP', 'EQ', 'Gen3', 'Gen4', 'Gen5', 'Polling', 'R', 'S_CFG_LINKWD_START', 'S_COMPL_EXIT_TX_EIDLE', 'S_DETECT_QUIET', 'S_DISABLED_IDLE', 'S_LPBK_ENTRY', 'S_POLL_ACTIVE', 'S_POLL_COMPLIANCE', 'S_PRE_DETECT_QUIET', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'S_RCVRY_EQ2', 'S_RCVRY_EQ3', 'S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'Speed', 'TP', 'ago', 'any_2_ts1_lpbk1_ebth1_rcvd_g5', 'at', 'b000', 'b001', 'b010', 'b011', 'b100', 'back', 'been', 'both', 'captured_lpbk_ts_data_rate', 'cfg_enter_compliance', 'cfg_pcie_max_link_speed', 'cfg_pl_gen3_zrxdc_noncompl', 'cfg_target_link_speed', 'change', 'changed_speed_recovery', 'clear', 'current_data_rate_d', 'done', 'ei_interval_expire', 'enter', 'eq', 'eq_to_rspeed_g4', 'eq_to_rspeed_g5', 'from', 'gen1', 'gen2', 'gen2_supported', 'gen3', 'gen3_speed_cmpl', 'gen3_supported', 'gen4', 'gen4_supported', 'gen5', 'gen5_supported', 'has', 'latched_eidle_sent', 'latched_l0_speed', 'latched_rcvd_eidle_set', 'latched_ts_data_rate', 'leaving', 'link_latched_live_all_2_ts1_lpbk1_rcvd', 'link_pc_rcvd', 'long', 'loopback', 'lpbk_master', 'lts_state', 'lts_state_d', 'ltssm_ts_data_rate', 'needed', 'next_data_rate', 'next_lts_state', 'precoding_on', 'rate', 'set', 'sides', 'skip_eq', 'smlh_successful_spd_negotiation', 'speed', 'support', 'to', 'transition', 'ts_to_poll_cmp_pulse', 'updated', 'updates', 'we', 'when', 'whenever']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #128  行号: 3286-3294
  写入信号: ['mac_phy_rate', 'mac_phy_rate1', 'mac_phy_rate2', 'mac_phy_rate3', 'mac_phy_rate4', 'mac_phy_rate_d']
  依赖信号: ['TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #129  行号: 3318-3321
  写入信号: ['latched_detect_speed', 'next_lts_state']
  依赖信号: ['TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #130  行号: 3328-3330
  写入信号: ['rate_change_flag']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #131  行号: 3338-3352
  写入信号: ['lts_state', 'n', 'smlh_rcvd_eidle_rxstandby']
  依赖信号: ['S_LPBK_ACTIVE', 'act_rmlh_gen3_rcvd_4eidle_set', 'act_rmlh_rcvd_eidle_set', 'clear', 'eidle_cnt_clear', 'g1_rate', 'g3_rate', 'g4_rate', 'g5_rate', 'h3', 'lpbk_master', 'lts_state', 'n', 'rcvd_eidle_cnt', 'rcvd_valid_eidle_set', 'smlh_rcvd_eidle_rxstandby']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #132  行号: 3363-3377
  写入信号: ['eios_subsequent_flag', 'n']
  依赖信号: ['SUBSEQ_TIMEOUT', 'TP', 'eios_subsequent_flag', 'int_rcvd_eidle_rxstandby', 'n']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #133  行号: 3379-3401
  写入信号: ['eios_subsequent_timer', 'n']
  依赖信号: ['TP', 'b0', 'eios_subsequent_timer', 'int_rcvd_eidle_rxstandby', 'n', 'timer2', 'timer_freq_multiplier']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #134  行号: 3404-3419
  写入信号: ['eios_l1_l2_flag', 'n']
  依赖信号: ['S_DISABLED', 'S_DISABLED_ENTRY', 'S_DISABLED_IDLE', 'S_L123_SEND_EIDLE', 'S_L1_IDLE', 'S_L2_IDLE', 'TP', 'eios_l1_l2_flag', 'lts_state', 'n']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #135  行号: 3426-3444
  写入信号: ['current_powerdown', 'lts_state', 'n', 'next_lts_state', 'next_ltssm_powerdown', 'r_curnt_l0s_rcv_state', 'set_rxstandby']
  依赖信号: ['EPX16_P1', 'EPX16_P2', 'S_CFG_IDLE', 'S_L0', 'S_L0S_RCV_IDLE', 'S_RCVRY_LOCK', 'b0', 'b1', 'clear', 'current_powerdown', 'eios_l1_l2_flag', 'eios_subsequent_flag', 'int_lanes_active_rxstandby', 'int_rcvd_eidle_rxstandby', 'laneflip_pipe_turnoff', 'latched_eidle_inferred', 'lts_state', 'n', 'next_lts_state', 'next_ltssm_powerdown', 'r_curnt_l0s_rcv_state', 'rate_change_flag', 'rxstandby_assertion_enable', 'set_rxstandby']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #136  行号: 3446-3460
  写入信号: ['eiexit_hs_in_progress', 'n']
  依赖信号: ['NL', 'TP', 'b0', 'b1', 'cfg_rxstandby_control', 'cfg_rxstandby_handshake_policy', 'eiexit_hs_in_progress', 'mac_phy_rxstandby', 'n', 'phy_mac_rxelecidle_noflip', 'phy_mac_rxstandbystatus', 'set_rxstandby']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #137  行号: 3462-3478
  写入信号: ['mac_phy_rxstandby', 'n']
  依赖信号: ['EPX16_CX_RXSTANDBY_DEFAULT', 'NL', 'TP', 'b1', 'mac_phy_rxstandby', 'n', 'phy_mac_rxstandbystatus', 'rxstandby_handshake_enable', 'set_rxstandby']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #138  行号: 3484-3681
  写入信号: ['cfg_pcie_max_link_speed', 'gen3_cmpl_tx_preset', 'gen3_speed_cmpl', 'gen4_cmpl_jmp', 'lts_state']
  依赖信号: ['EPX16_GEN2_LINK_SP', 'EPX16_GEN3_LINK_SP', 'EPX16_GEN4_LINK_SP', 'S_POLL_COMPLIANCE', 'S_POLL_CONFIG', 'Setting', 'TP', 'b000', 'b0000', 'b0001', 'b001', 'b0010', 'b0011', 'b010', 'b0100', 'b0101', 'b011', 'b0110', 'b0111', 'b100', 'b1000', 'b1001', 'b1010', 'cfg_pcie_max_link_speed', 'cfg_pl_gen3_zrxdc_noncompl', 'clear', 'from', 'gen2', 'gen3_cmpl_tx_preset', 'gen3_speed_cmpl', 'gen4', 'gen4_cmpl_jmp', 'gen5', 'go', 'ltssm_ts_data_rate', 'next_lts_state', 'no', 'only', 'speed', 'support', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #139  行号: 3692-3701
  写入信号: ['latched_enter_compliance_8ts1', 'lts_state', 'next_lts_state']
  依赖信号: ['S_POLL_ACTIVE', 'S_POLL_COMPLIANCE', 'TP', 'clear', 'latched_enter_compliance_8ts1', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #140  行号: 3712-3730
  写入信号: ['latched_link_retrain_bit', 'latched_perform_eq', 'latched_target_link_speed']
  依赖信号: ['TP', 'b0', 'b1', 'cfg_perform_eq', 'cfg_target_link_speed', 'latched_link_retrain_bit', 'latched_perform_eq', 'latched_target_link_speed']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #141  行号: 3738-3746
  写入信号: ['ltssm_core_rst_n_release', 'ltssm_core_rst_n_release_d']
  依赖信号: ['TP', 'ltssm_core_rst_n_release']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #142  行号: 3752-3760
  写入信号: ['clear_d', 'directed_speed_change_d']
  依赖信号: ['TP', 'clear', 'directed_speed_change']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #143  行号: 3770-3797
  写入信号: ['cfg_pcie_max_link_speed', 'cfg_select_deemph_var_mux', 'last_lts_state', 'lts_state', 'next_lts_state', 'select_deemphasis', 'variable']
  依赖信号: ['A13', 'Compliance', 'Control', 'DSP', 'De', 'EPX16_GEN3_LINK_SP', 'Else', 'Errata', 'Gen2', 'Link', 'Polling', 'RcvrCfg', 'Recovery', 'S_CFG_LINKWD_START', 'S_DETECT_QUIET', 'S_LPBK_ENTRY', 'S_POLL_ACTIVE', 'S_POLL_COMPLIANCE', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'Selectable', 'TP', 'The', 'USP', 'any_8_ts_linknmtx_lanenmtx_rcvd', 'at', 'b1', 'bit', 'by', 'captured_lpbk_ts_auto_change', 'cfg_compliance_de_emphasis', 'cfg_enter_compliance', 'cfg_gen2_support', 'cfg_pcie_max_link_speed', 'cfg_sel_de_emphasis', 'cfg_select_deemph_var_mux', 'cfg_upstream_port', 'clear', 'de', 'emphasis', 'entry', 'field', 'g2_rate', 'gen2_deemphasis', 'in', 'is', 'it', 'keep', 'last_lts_state', 'latched_ts_deemphasis_var', 'line', 'link_any_8_ts_linknmtx_lanenmtx_auto_chg_rcvd', 'link_latched_live_all_2_ts1_lpbk1_rcvd', 'logic', 'lpbk_master', 'lts_state', 'next', 'next_lts_state', 'no', 'on', 'rate', 'records', 'register', 'requested', 'select_deemphasis', 'state', 'the', 'timeout_24ms', 'to', 'ts_to_poll_cmp_pulse', 'update', 'value', 'variable']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #144  行号: 3800-3802
  写入信号: ['latched_l0_speed']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #145  行号: 3808-3833
  写入信号: ['current_data_rate', 'eq_to_rspeed_g4', 'eq_to_rspeed_g5', 'lts_state', 'next_lts_state', 'smlh_successful_spd_negotiation']
  依赖信号: ['EPX16_GEN4_RATE', 'EPX16_GEN5_RATE', 'Gen2', 'RcvrLock', 'Recovery', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'S_RCVRY_EQ2', 'S_RCVRY_EQ3', 'S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'Switch', 'TP', 'b0', 'b1', 'bypass_g3_eq', 'bypass_g4_eq', 'clear', 'current_data_rate', 'entry', 'eq_to_rspeed_g5', 'goe_g5', 'link_latched_live_any_ts2_rcvd', 'lts_state', 'next_lts_state', 'on', 'reset', 'skip_eq', 'smlh_successful_spd_negotiation', 'speed', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #146  行号: 3878-3894
  写入信号: ['directed_speed_change', 'lts_state', 'next_lts_state']
  依赖信号: ['S_L0', 'S_L0S', 'S_L1_IDLE', 'S_RCVRY_LOCK', 'TP', 'b0', 'clear', 'directed_speed_change', 'g3_rate', 'g4_rate', 'g5_rate', 'lts_state', 'next_lts_state', 'redo_eq_phase_g3', 'redo_eq_phase_g4', 'redo_eq_phase_g5', 'redo_eq_phase_step']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #147  行号: 3913-3916
  写入信号: ['lts_state', 'ltssm_ts_spd_chg_rcvd_clr_int']
  依赖信号: ['b0']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #148  行号: 3925-3931
  写入信号: ['ltssm_ts_spd_chg_rcvd_clr']
  依赖信号: ['ltssm_ts_spd_chg_rcvd_clr_int']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #149  行号: 3933-3942
  写入信号: ['directed_speed_change_r', 'lts_state', 'lts_state_d']
  依赖信号: ['S_CFG_LINKWD_START', 'S_L0', 'S_L0S', 'S_L1_IDLE', 'S_PRE_DETECT_QUIET', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'S_RCVRY_SPEED', 'TP', 'b0', 'b1', 'cfg_directed_speed_change', 'clear', 'directed_speed_change', 'directed_speed_change_r', 'lts_state', 'lts_state_d', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #150  行号: 3945-3947
  写入信号: ['changed_speed_recovery']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #151  行号: 3960-3962
  写入信号: ['ltssm_ts_speed_change']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #152  行号: 3973-3975
  写入信号: ['ltssm_ts_auto_change']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #153  行号: 4000-4003
  写入信号: ['lts_state', 'ltssm_ts_data_rate_int', 'next_lts_state']
  依赖信号: ['TP', 'b00001']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #154  行号: 4012-4014
  写入信号: ['ltssm_ts_data_rate']
  依赖信号: ['TP', 'b00001']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #155  行号: 4068-4083
  写入信号: ['directed_link_width_change', 'latched_target_link_width', 'latched_valid_reliability_link_width_change', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_IDLE', 'S_L0', 'S_L1_IDLE', 'S_RCVRY_LOCK', 'TP', 'b0', 'b1', 'clear', 'directed_link_width_change', 'go_recovery_link_width_change', 'h0', 'int_target_link_width_real', 'latched_target_link_width', 'lts_state', 'next_lts_state', 'valid_reliability_link_width_change']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #156  行号: 4085-4092
  写入信号: ['latched_auto_width_downsizing']
  依赖信号: ['TP', 'directed_link_width_change_down', 'latched_auto_width_downsizing']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #157  行号: 4094-4099
  写入信号: ['hw_autowidth_dis_d']
  依赖信号: ['TP', 'cfg_hw_autowidth_dis']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #158  行号: 4103-4110
  写入信号: ['hw_autowidth_dis_upconf', 'linkup_link_mode', 'next_lts_state']
  依赖信号: ['S_L0', 'TP', 'clear', 'directed_link_width_change_up', 'hw_autowidth_dis_upconf', 'latched_target_link_width', 'linkup_link_mode', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #159  行号: 4127-4133
  写入信号: ['directed_link_width_change_d']
  依赖信号: ['TP', 'directed_link_width_change']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #160  行号: 4142-4154
  写入信号: ['cfglwstart_upconf_dsp', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'TP', 'b0', 'b1', 'cfg_upstream_port', 'cfglwstart_upconf_dsp', 'clear', 'directed_link_width_change_up', 'link_latched_live_all_2_ts1_plinkn_planen_rcvd', 'next_lts_state', 'timeout_1ms']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #161  行号: 4156-4166
  写入信号: ['i', 'latchd_rxeidle_exit_upconf', 'lts_state', 'next_lts_state']
  依赖信号: ['S_L0', 'S_L1_IDLE', 'S_RCVRY_LOCK', 'TP', 'b1', 'clear', 'i', 'latchd_rxeidle_exit_upconf', 'lts_state', 'phy_mac_rxelecidle']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #162  行号: 4168-4175
  写入信号: ['lts_state', 'upconfigure_capable']
  依赖信号: ['S_CFG_COMPLETE', 'S_DETECT_QUIET', 'TP', 'clear', 'link_ln0_8_ts2_linknmtx_lanenmtx_auto_chg_rcvd', 'link_ln0_8_ts2_linknmtx_lanenmtx_rcvd', 'ltssm_ts_auto_change', 'upconfigure_capable']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #163  行号: 4190-4194
  写入信号: ['smlh_retimer_pre_detected', 'smlh_two_retimers_pre_detected']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #164  行号: 4200-4202
  写入信号: ['persist_2scrmb_dis_rcvd']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #165  行号: 4214-4216
  写入信号: ['eidle_continuity_cnt']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #166  行号: 4222-4321
  写入信号: ['eidle_continuity_cnt']
  依赖信号: ['TP', 'active_nb', 'b0', 'b1', 'd1', 'd3', 'eidle_continuity_cnt']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #167  行号: 4324-4373
  写入信号: ['rcvd_valid_eidle_set']
  依赖信号: ['act_rmlh_rcvd_eidle_set', 'active_nb', 'eidle_continuity_cnt', 'rcvd_eidle_cnt']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #168  行号: 4385-4451
  写入信号: ['eidle_cnt_clear']
  依赖信号: ['act_rmlh_rcvd_eidle_set', 'active_nb', 'd0', 'eidle_continuity_cnt', 'rcvd_eidle_cnt']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #169  行号: 4457-4573
  写入信号: ['rcvd_eidle_cnt']
  依赖信号: ['TP', 'b0', 'b1', 'rcvd_eidle_cnt']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #170  行号: 4575-4741
  写入信号: ['clked_app_init_rst', 'clked_cfg_link_dis', 'clked_cfg_lpbk_en', 'clked_cfg_reset_assert', 'curnt_compliance_state', 'direct_rst', 'direct_rst_d', 'gointo_rcovr_state_d', 'idle_16_sent', 'ii', 'int_latched_smlh_inskip_rcv', 'j', 'latched_all_smlh_sds_rcvd', 'latched_cfg_link_dis', 'latched_direct_rst', 'latched_eidle_sent', 'latched_link_retrain', 'latched_rcvd_eidle_set', 'latched_rec_cfg_to_l0', 'latched_smlh_inskip_rcv', 'latched_smlh_sds_rcvd', 'lpbk_master', 'lts_state', 'next_lts_state', 'r_curnt_l0s_rcv_state', 'rcvd_4eidle', 'retrain_complete', 'retrain_pulse', 'smlh_scrambler_disable', 'up_rst_deassert']
  依赖信号: ['Configuration', 'EIOS', 'Gen3', 'L0', 'Recovery', 'S_CFG_IDLE', 'S_COMPL_TX_COMPLIANCE', 'S_DETECT_QUIET', 'S_DISABLED_IDLE', 'S_HOT_RESET', 'S_HOT_RESET_ENTRY', 'S_L0', 'S_L0S_RCV_ENTRY', 'S_L0S_RCV_IDLE', 'S_L123_SEND_EIDLE', 'S_LPBK_ENTRY', 'S_LPBK_EXIT_TIMEOUT', 'S_RCVRY_IDLE', 'Spec', 'TP', 'app_init_rst', 'as', 'b0', 'b1', 'by', 'capture', 'cfg_link_dis', 'cfg_link_retrain', 'cfg_lpbk_en', 'cfg_reset_assert', 'cfg_upstream_port', 'clear', 'clked_app_init_rst', 'clked_cfg_reset_assert', 'direct_rst', 'from', 'g1_rate', 'g2_rate', 'gointo_rcovr_state_d', 'has', 'ii', 'int_latched_smlh_inskip_rcv', 'j', 'latched_direct_rst', 'latched_eidle_sent', 'latched_link_retrain', 'latched_rcvd_eidle_set', 'latched_rec_cfg_to_l0', 'latched_smlh_sds_rcvd', 'linkdown', 'lpbk_master', 'lts_state', 'ltssm_lanes_active', 'next_lts_state', 'persist_2scrmb_dis_rcvd', 'r_curnt_l0s_rcv_state', 'rcvd_4eidle', 'rcvd_eidle_cnt', 's', 'smlh_inskip_rcv', 'smlh_sds_rcvd', 'symbols', 'the', 'to', 'transition', 'well']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #171  行号: 4749-4763
  写入信号: ['latched_only_rdlh_rcvd_dllp', 'latched_rdlh_rcvd_dllp']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #172  行号: 4786-4797
  写入信号: ['auto_eq_phase_flag_ff', 'lts_state']
  依赖信号: ['S_LPBK_ACTIVE', 'TP', 'after', 'any', 'auto_eq_phase_flag_ff', 'b0', 'b1', 'by', 'done', 'eq', 'gen4', 'gen5', 'is', 'sides', 'supported', 'unset']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #173  行号: 4826-4839
  写入信号: ['soft_eq_phase_flag_ff']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #174  行号: 4856-4868
  写入信号: ['soft_gen4_eq_phase_flag_ff']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #175  行号: 4886-4896
  写入信号: ['redo_eq_phase_g3_ff']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #176  行号: 4911-4921
  写入信号: ['redo_eq_phase_g4_ff']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #177  行号: 4936-4946
  写入信号: ['redo_eq_phase_g5_ff']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #178  行号: 4985-4991
  写入信号: 无
  依赖信号: ['TP', 'redo_eq_phase_step']
  所有写入信号单驱动: False
  推荐解耦等级: 低（无寄存器写入，或仅组合逻辑）
------------------------------------------------------------
Always块 #179  行号: 5023-5027
  写入信号: ['init_eq_pending', 'init_usp_eq_pending']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #180  行号: 5045-5046
  写入信号: 无
  依赖信号: 无
  所有写入信号单驱动: False
  推荐解耦等级: 低（无寄存器写入，或仅组合逻辑）
------------------------------------------------------------
Always块 #181  行号: 5069-5081
  写入信号: ['latched_remote_soft_eq_g4']
  依赖信号: ['TP', 'b0', 'b1', 'latched_remote_soft_eq_g4']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #182  行号: 5085-5086
  写入信号: 无
  依赖信号: 无
  所有写入信号单驱动: False
  推荐解耦等级: 低（无寄存器写入，或仅组合逻辑）
------------------------------------------------------------
Always块 #183  行号: 5112-5124
  写入信号: ['latched_remote_soft_eq_g5']
  依赖信号: ['TP', 'b0', 'b1', 'latched_remote_soft_eq_g5']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #184  行号: 5129-5138
  写入信号: ['latched_idle_to_rcvrylock', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_IDLE', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'TP', 'clear', 'latched_idle_to_rcvrylock', 'lts_state', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #185  行号: 5141-5153
  写入信号: ['lts_state', 'rlock_ts1_cnt']
  依赖信号: ['S_RCVRY_LOCK', 'TP', 'rlock_ts1_cnt', 'xmtbyte_ts1_sent']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #186  行号: 5156-5158
  写入信号: ['latched_rec_to_cfg']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #187  行号: 5166-5171
  写入信号: ['current_data_rate_d']
  依赖信号: ['TP', 'current_data_rate']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #188  行号: 5173-5180
  写入信号: ['latched_rate_change']
  依赖信号: ['TP', 'b1', 'current_data_rate', 'latched_rate_change']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #189  行号: 5182-5191
  写入信号: ['latched_g3_auto_bw_status']
  依赖信号: ['TP', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #190  行号: 5195-5204
  写入信号: ['latched_g4_auto_bw_status']
  依赖信号: ['TP', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #191  行号: 5232-5234
  写入信号: ['smlh_tx_margin_rst']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #192  行号: 5254-5261
  写入信号: ['latched_first_entry_rcvry_lock', 'lts_state']
  依赖信号: ['S_DETECT_QUIET', 'TP', 'latched_first_entry_rcvry_lock']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #193  行号: 5263-5272
  写入信号: ['mac_phy_txmargin', 'next_lts_state']
  依赖信号: ['S_POLL_ACTIVE', 'TP', 'cfg_enter_compliance', 'cfg_transmit_margin', 'clear', 'mac_phy_txmargin']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #194  行号: 5279-5285
  写入信号: ['mac_phy_txswing']
  依赖信号: ['EPX16_DEFAULT_GEN2_TXSWING', 'S_DETECT_QUIET', 'TP', 'cfg_phy_txswing', 'mac_phy_txswing']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #195  行号: 5293-5295
  写入信号: ['xmt_ts_lnknum']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #196  行号: 5304-5312
  写入信号: ['latched_xmt_ts_lnknum']
  依赖信号: ['TP', 'cfg_link_num', 'xmt_ts_lnknum']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #197  行号: 5323-5328
  写入信号: ['last_lts_state', 'latched_no_idle_need_sent', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_LINKWD_START', 'S_LPBK_ENTRY', 'S_RCVRY_IDLE', 'TP', 'cfg_lpbk_en', 'clear', 'last_lts_state', 'latched_no_idle_need_sent', 'loopback', 'lts_state', 'master', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #198  行号: 5333-5352
  写入信号: ['lts_state', 'ltssm_ts_cnt_en']
  依赖信号: ['S_CFG_LINKWD_START', 'S_POLL_ACTIVE', 'S_POLL_CONFIG', 'S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'TP', 'b0', 'b1', 'lts_state', 'ltssm_ts_cnt_en']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #199  行号: 5364-5372
  写入信号: ['latched_first_rcvd_ts2', 'lts_state']
  依赖信号: ['S_CFG_COMPLETE', 'S_RCVRY_RCVRCFG', 'TP', 'in', 'latched_first_rcvd_ts2', 'lts_state', 'reset', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #200  行号: 5374-5382
  写入信号: ['latched_first_sent_ts', 'lts_state']
  依赖信号: ['S_CFG_COMPLETE', 'S_RCVRY_RCVRCFG', 'TP', 'in', 'latched_first_sent_ts', 'lts_state', 'reset', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #201  行号: 5400-5411
  写入信号: ['directed_equalization_g3']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #202  行号: 5421-5432
  写入信号: ['directed_equalization_g4_skipeq']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #203  行号: 5435-5446
  写入信号: ['directed_equalization_g4']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #204  行号: 5454-5465
  写入信号: ['directed_equalization_g5']
  依赖信号: ['TP', 'b0', 'b1']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #205  行号: 5472-5518
  写入信号: ['lts_state', 'next_lts_state', 'start_equalization_w_preset', 'state', 'target_link_speed']
  依赖信号: ['Else', 'Gen3', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'TP', 'This', 'any', 'b0', 'b1', 'captured_ts_data_rate', 'cfg_gen3_eq_disable', 'check', 'clear', 'downstream_component', 'eq', 'eqctl_any_8eqts2_rcvd', 'gen4_supported', 'gen5', 'gen5_supported', 'go', 'in', 'link_latched_live_any_ts2_rcvd', 'lts_state', 'need', 'needs', 'next', 'next_lts_state', 'noeq_nd', 'only', 'received', 'set', 'skip_eq', 'start_equalization_w_preset', 'state', 'to', 'ts2']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #206  行号: 5521-5542
  写入信号: ['equalization_done_8gt_data_rate', 'lts_state', 'next_lts_state', 'no_eq_needed']
  依赖信号: ['S_DETECT_QUIET', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'TP', 'b0', 'b1', 'clear', 'dsp_skip_eq_falling_edge', 'equalization_done_8gt_data_rate', 'lts_state', 'set', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #207  行号: 5545-5566
  写入信号: ['equalization_done_16gt_data_rate', 'lts_state', 'next_lts_state', 'no_eq_needed']
  依赖信号: ['S_DETECT_QUIET', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'TP', 'b0', 'b1', 'clear', 'dsp_skip_eq_falling_edge', 'equalization_done_16gt_data_rate', 'lts_state', 'set', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #208  行号: 5569-5587
  写入信号: ['equalization_done_32gt_data_rate', 'lts_state', 'next_lts_state', 'no_eq_needed']
  依赖信号: ['S_DETECT_QUIET', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'TP', 'b0', 'b1', 'cfg_directed_speed_change', 'cfg_link_retrain', 'cfg_perform_eq', 'clear', 'dsp_skip_eq_falling_edge', 'equalization_done_32gt_data_rate', 'lts_state', 'ltssm_ts_data_rate', 'set', 'skip_eq', 'slr_g3', 'to']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #209  行号: 5591-5598
  写入信号: ['lts_state', 'ltssm_cmd_eqts']
  依赖信号: ['S_RCVRY_RCVRCFG', 'TP', 'b0', 'b1', 'ltssm_cmd_eqts']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #210  行号: 5616-5623
  写入信号: ['eqctl_any_8eqts1_rcvd_d', 'lts_state']
  依赖信号: ['S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'TP', 'eqctl_any_8eqts1_rcvd', 'eqctl_any_8eqts1_rcvd_d', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #211  行号: 5627-5637
  写入信号: ['lts_state_d', 'ltssm_cmd_eqts_gen12']
  依赖信号: ['EQ', 'Gen5', 'Rx', 'S_RCVRY_RCVRCFG', 'TS2s', 'as', 'b00', 'b01', 'b10', 'eqctl_any_8eqts2_rcvd', 'gen1', 'initial', 'ltssm_cmd_eqts_gen12', 'preset', 'the', 'use', 'with']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #212  行号: 5644-5645
  写入信号: 无
  依赖信号: 无
  所有写入信号单驱动: False
  推荐解耦等级: 低（无寄存器写入，或仅组合逻辑）
------------------------------------------------------------
Always块 #213  行号: 5657-5665
  写入信号: ['latchecd_eqctl_8g4eqts1_rcvd', 'lts_state']
  依赖信号: ['S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'TP', 'b0', 'eqctl_8g4eqts1_rcvd', 'latchecd_eqctl_8g4eqts1_rcvd', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #214  行号: 5670-5674
  写入信号: ['lts_state', 'ltssm_cmd_8geqts']
  依赖信号: ['b0']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #215  行号: 5686-5696
  写入信号: ['lts_state', 'ltssm_cmd_eqredo']
  依赖信号: ['S_RCVRY_LOCK', 'TP', 'b0', 'b1', 'ltssm_cmd_eqredo']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #216  行号: 5698-5706
  写入信号: ['latchecd_eqctl_8g5eqts1_rcvd', 'lts_state']
  依赖信号: ['S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'TP', 'b0', 'eqctl_8g5eqts1_rcvd', 'latchecd_eqctl_8g5eqts1_rcvd', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #217  行号: 5711-5715
  写入信号: ['lts_state', 'ltssm_cmd_16geqts']
  依赖信号: ['b0']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #218  行号: 5747-5749
  写入信号: ['l0s_link_rcvry_en']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #219  行号: 5765-5771
  写入信号: ['common_gen1_supported', 'lts_state']
  依赖信号: ['S_CFG_COMPLETE', 'S_RCVRY_RCVRCFG', 'TP', 'clear', 'common_gen1_supported', 'gen2_supported', 'lts_state', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #220  行号: 5803-5820
  写入信号: ['lts_state', 'ltssm_in_training']
  依赖信号: ['S_CFG_COMPLETE', 'S_CFG_IDLE', 'S_CFG_LANENUM_ACEPT', 'S_CFG_LANENUM_WAIT', 'S_CFG_LINKWD_ACEPT', 'S_CFG_LINKWD_START', 'S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'S_RCVRY_EQ2', 'S_RCVRY_EQ3', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'S_RCVRY_SPEED', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #221  行号: 5822-5829
  写入信号: ['lts_state', 'ltssm_state_rcvry_eq']
  依赖信号: ['S_RCVRY_EQ0', 'S_RCVRY_EQ1', 'S_RCVRY_EQ2', 'S_RCVRY_EQ3', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #222  行号: 5833-5845
  写入信号: ['latched_rcvd_2_unexpect_ts', 'lts_state']
  依赖信号: ['S_L0', 'TP', 'latched_rcvd_2_unexpect_ts']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #223  行号: 5849-5851
  写入信号: ['l0_link_rcvry_en']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #224  行号: 5872-5888
  写入信号: ['lts_state', 'next_lts_state', 'no_idle_need_sent']
  依赖信号: ['S_RCVRY_IDLE', 'TP', 'cfg_gointo_cfg_state', 'cfg_link_dis', 'cfg_lpbk_en', 'cfg_upstream_port', 'clear', 'directed_link_width_change', 'latched_direct_rst', 'next_lts_state', 'no_idle_need_sent']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #225  行号: 5893-5900
  写入信号: ['ltssm_cmd', 'ltssm_eidle_cnt', 'ltssm_xk237_4lannum', 'ltssm_xk237_4lnknum', 'ltssm_xlinknum']
  依赖信号: ['EPX16_XMT_IN_EIDLE', 'NL', 'TP', 'b1', 'd0']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #226  行号: 6131-6138
  写入信号: ['current_powerdown_p0_d', 'next_lts_state']
  依赖信号: ['S_POLL_ACTIVE', 'S_RCVRY_LOCK', 'TP', 'clear', 'current_powerdown_p0', 'current_powerdown_p0_d', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #227  行号: 6149-6167
  写入信号: ['curnt_compliance_state', 'current_powerdown', 'l0s_state_clear', 'lts_state', 'r_clear']
  依赖信号: ['CLEAR_WD', 'Clear', 'P0', 'PIPE', 'S_COMPL_ENT_TX_EIDLE', 'S_COMPL_EXIT_TX_EIDLE', 'S_LPBK_ENTRY', 'S_POLL_ACTIVE', 'S_RCVRY_LOCK', 'TP', 'a', 'according', 'app_ltssm_enable_fall_edge', 'b1', 'change', 'clear', 'counters', 'curnt_compliance_state', 'curnt_compliance_state_d1', 'curnt_l0s_xmt_state', 'current_powerdown_p0_rising_edge', 'during', 'edge', 'few', 'in', 'involved', 'is', 'l0s_state_clear', 'lpbk_clear', 'lts_state', 'next_l0s_xmt_state', 'next_lts_state', 'on', 'power', 'rising', 'spec', 'state', 'states', 'that']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #228  行号: 6170-6174
  写入信号: ['latched_next_lts_flag', 'latched_next_lts_state']
  依赖信号: ['S_DETECT_QUIET', 'TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #229  行号: 6189-6191
  写入信号: ['lts_state_wire']
  依赖信号: ['S_PRE_DETECT_QUIET']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #230  行号: 6211-6229
  写入信号: ['lts_state', 'smlh_ltssm_state', 'smlh_ltssm_state_rmlh', 'smlh_ltssm_state_smlh_eq', 'smlh_ltssm_state_smlh_lnk', 'smlh_ltssm_state_smlh_sqf', 'smlh_ltssm_state_xmlh']
  依赖信号: ['S_DETECT_QUIET', 'TP', 'lts_state_wire']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #231  行号: 6232-6234
  写入信号: ['last_lts_state']
  依赖信号: ['S_DETECT_QUIET', 'TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #232  行号: 6245-6247
  写入信号: ['error_entr_l1']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #233  行号: 6253-6260
  写入信号: ['ds_timeout_2ms', 'lts_state']
  依赖信号: ['S_HOT_RESET_ENTRY', 'S_PRE_DETECT_QUIET', 'TP', 'b0', 'b1', 'cfg_upstream_port', 'clear', 'ds_timeout_2ms', 'timeout_2ms']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #234  行号: 6263-6274
  写入信号: ['latched_to_idle_timeout', 'lts_state', 'next_lts_state']
  依赖信号: ['S_CFG_COMPLETE', 'S_CFG_IDLE', 'S_RCVRY_IDLE', 'S_RCVRY_RCVRCFG', 'TP', 'b0', 'b1', 'clear', 'latched_to_idle_timeout', 'lts_state', 'next_lts_state', 'timeout_2ms', 'timeout_48ms']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #235  行号: 6280-6287
  写入信号: ['pset_map_eieos_cnt']
  依赖信号: ['TP', 'pset_map_eieos_cnt']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #236  行号: 6290-6290
  写入信号: 无
  依赖信号: 无
  所有写入信号单驱动: False
  推荐解耦等级: 低（无寄存器写入，或仅组合逻辑）
------------------------------------------------------------
Always块 #237  行号: 6301-6309
  写入信号: ['latched_xmtbyte_eies_sent']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #238  行号: 6313-6321
  写入信号: ['cfgcmpl_all_8_ts2_rcvd', 'int_bypass_gen3_eq', 'int_bypass_gen4_eq', 'int_ltssm_cmd_send_eieos_for_pset_map', 'int_rcvd_8_ts2_noeq_nd', 'int_rcvd_8_ts2_skip_eq', 'int_rcvd_8expect_ts1', 'ltssm_in_hotrst_dis_entry']
  依赖信号: ['b0', 'bypass_g3_eq', 'bypass_g4_eq']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #239  行号: 7050-7055
  写入信号: ['cfg_upstream_port_d']
  依赖信号: ['TP', 'cfg_upstream_port']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #240  行号: 7058-7080
  写入信号: ['equalization_done_16gt_data_rate', 'equalization_done_16gt_data_rate_d', 'equalization_done_32gt_data_rate', 'equalization_done_32gt_data_rate_d', 'equalization_done_8gt_data_rate', 'equalization_done_8gt_data_rate_d', 'lts_state']
  依赖信号: ['S_RCVRY_EQ0', 'TP', 'clear', 'equalization_done_16gt_data_rate', 'equalization_done_16gt_data_rate_d', 'equalization_done_32gt_data_rate', 'equalization_done_32gt_data_rate_d', 'equalization_done_8gt_data_rate', 'equalization_done_8gt_data_rate_d', 'next_lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #241  行号: 7091-7100
  写入信号: ['lts_state', 'smlh_link_up']
  依赖信号: ['S_CFG_IDLE', 'S_L0', 'S_L0S', 'S_LPBK_ACTIVE', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'TP', 'b0', 'b1', 'lpbk_master', 'lts_state', 'smlh_link_up']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #242  行号: 7116-7131
  写入信号: ['lts_state', 'smlh_req_rst_not']
  依赖信号: ['S_CFG_IDLE', 'S_DETECT_ACT', 'S_DETECT_QUIET', 'S_DETECT_WAIT', 'S_L0', 'S_L0S', 'S_LPBK_ACTIVE', 'S_POLL_COMPLIANCE', 'S_POLL_CONFIG', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'S_RCVRY_RCVRCFG', 'TP', 'b0', 'b1', 'lpbk_master', 'lts_state', 'smlh_req_rst_not']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #243  行号: 7146-7163
  写入信号: ['lts_state', 'perform_eq_for_loopback', 'perform_eq_for_lpbk_mstr']
  依赖信号: ['Detect', 'Loopback', 'S_DETECT_QUIET', 'TP', 'b0', 'b1', 'because', 'clear', 'perform_eq_for_loopback']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #244  行号: 7167-7175
  写入信号: ['lts_state', 'tx_mod_cmpl_pattern_in_lpbk']
  依赖信号: ['Detect', 'Loopback', 'S_DETECT_QUIET', 'TP', 'b0', 'b1', 'because', 'clear', 'tx_mod_cmpl_pattern_in_lpbk']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #245  行号: 7177-7185
  写入信号: ['lpbk', 'lpbk_master_eq_exit']
  依赖信号: ['Entry', 'Lpbk', 'TP', 'active', 'b0', 'b1', 'in', 'is', 'lpbk', 'lpbk_master_eq_exit', 'means', 'slave']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #246  行号: 7202-7217
  写入信号: ['int_alt_protocol', 'int_disable', 'int_flit_mode', 'int_hot_reset', 'int_lpbk', 'int_lpbk_eq', 'int_mod_ts', 'int_no_eq_needed', 'int_skip_eq', 'int_tx_mod_cmpl', 'lpbk', 'lts_state', 'ltssm_in_pollconfig']
  依赖信号: ['S_POLL_CONFIG', 'TP', 'b0', 'lts_state']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #247  行号: 7237-7246
  写入信号: ['lts_state', 'mod_ts_for_ts2_lid_deskew']
  依赖信号: ['S_CFG_IDLE', 'S_DETECT_QUIET', 'S_L0', 'S_RCVRY_LOCK', 'b1', 'cfg_ts2_lid_deskew', 'clear', 'lts_state', 'ltssm_mod_ts', 'mod_ts_for_ts2_lid_deskew', 'mod_ts_for_ts2_lid_deskew_d', 'next_lts_state', 'smlh_link_up']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #248  行号: 7248-7253
  写入信号: ['mod_ts_for_ts2_lid_deskew_d']
  依赖信号: ['TP', 'mod_ts_for_ts2_lid_deskew']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #249  行号: 7255-7260
  写入信号: ['ltssm_mod_ts']
  依赖信号: ['TP', 'config_lanenum_state', 'gen12', 'mod_ts_for_ts2_lid_deskew', 'smlh_link_up', 'use_modified_ts_d']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #250  行号: 7262-7266
  写入信号: ['lts_state', 'ltssm_mod_ts_rx']
  依赖信号: ['S_CFG_LINKWD_ACEPT', 'TP', 'config_lanenum_state', 'gen12', 'lts_state', 'mod_ts_for_ts2_lid_deskew', 'rx_use_modified_ts_d', 'smlh_link_up']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #251  行号: 7269-7273
  写入信号: ['int_compliance_rcv', 'ltssm_cmd']
  依赖信号: ['EPX16_SEND_TS2', 'cfg_tx_compliance_rcv', 'ltssm_cmd']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #252  行号: 7289-7291
  写入信号: ['curnt_compliance_state']
  依赖信号: ['S_COMPL_IDLE', 'TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #253  行号: 7297-7299
  写入信号: ['curnt_compliance_state_d1']
  依赖信号: ['S_COMPL_IDLE', 'TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #254  行号: 7303-7317
  写入信号: ['next_compliance_state']
  依赖信号: ['S_COMPL_ENT_TX_EIDLE']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #255  行号: 7374-7376
  写入信号: ['curnt_lpbk_entry_state']
  依赖信号: ['S_LPBK_ENTRY_IDLE', 'TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #256  行号: 7382-7388
  写入信号: ['lpbk_clear_wire']
  依赖信号: ['b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #257  行号: 7418-7420
  写入信号: ['lpbk_clear']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #258  行号: 7429-7437
  写入信号: ['curnt_l0s_xmt_state', 'xmt_timer_20ns']
  依赖信号: ['S_L0S_XMT_EIDLE', 'TP', 'b0', 'timer2', 'timer_freq_multiplier', 'xmt_timer_20ns']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #259  行号: 7441-7443
  写入信号: ['curnt_l0s_xmt_state']
  依赖信号: ['S_L0S_XMT_ENTRY', 'TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #260  行号: 7448-7456
  写入信号: ['lts_state', 'next_l0s_xmt_state']
  依赖信号: ['L0s', 'S_L0', 'S_L0S', 'S_L0S_XMT_EIDLE', 'enter', 'first', 'in', 'l0_link_rcvry_en', 'l0s', 'lts_state', 'next_l0s_xmt_state', 'pm_smlh_entry_to_l0s', 'pm_smlh_prepare4_l123', 'rcvr', 'rcvr_l0s_goto_rcvry', 'when', 'xmtr']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #261  行号: 7503-7512
  写入信号: ['r_l0s_rcv_entry', 'r_l0s_rcv_fts', 'r_l0s_rcv_idle']
  依赖信号: ['TP', 'l0s_rcv_entry', 'l0s_rcv_fts', 'l0s_rcv_idle']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #262  行号: 7523-7525
  写入信号: ['smlh_pm_latched_eidle_set']
  依赖信号: ['TP', 'b0']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #263  行号: 7536-7550
  写入信号: ['latched_rcvd_eidle_set_4rl0s', 'lts_state']
  依赖信号: ['S_CFG_IDLE', 'S_CFG_LINKWD_START', 'S_DETECT_QUIET', 'S_L0', 'S_L0S', 'S_RCVRY_IDLE', 'S_RCVRY_LOCK', 'TP', 'b0', 'b1', 'l0s_rcv_idle', 'latched_rcvd_eidle_set_4rl0s', 'lts_state', 'r_l0s_rcv_idle']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #264  行号: 7552-7589
  写入信号: ['curnt_l0s_rcv_state', 'lts_state', 'period']
  依赖信号: ['L0s', 'S_L0', 'S_L0S', 'S_L0S_RCV_ENTRY', 'S_L0S_RCV_FTS', 'S_L0S_RCV_IDLE', 'When', 'active', 'all', 'cfg_l0s_supported', 'curnt_l0s_rcv_state', 'detect', 'gen3', 'here', 'in', 'l0', 'l0_link_rcvry_en', 'lanes', 'latched_all_smlh_sds_rcvd', 'latched_rcvd_eidle_set_4rl0s', 'latched_smlh_inskip_rcv', 'lts_state', 'need', 'on', 'pm_smlh_prepare4_l123', 'rcvr_l0s_goto_rcvry', 'received', 'rmlh_deskew_complete', 'roughly', 'sds', 'state', 'this', 'timeout_40ns_4rl0s', 'timeout_nfts', 'timer', 'use', 'we', 'xmtr']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #265  行号: 7592-7594
  写入信号: ['r_curnt_l0s_rcv_state']
  依赖信号: ['S_L0S_RCV_ENTRY', 'TP']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #266  行号: 7599-7610
  写入信号: ['lts_state', 'r_curnt_l0s_rcv_state', 'rcvr_l0s_goto_rcvry']
  依赖信号: ['L0s', 'S_L0', 'S_L0S', 'S_L0S_RCV_ENTRY', 'S_L0S_RCV_FTS', 'S_L0S_XMT_ENTRY', 'S_RCVRY_LOCK', 'TP', 'at', 'b0', 'b1', 'before', 'curnt_l0s_xmt_state', 'l0s_link_rcvry_en', 'lts_state', 'nfts', 'r_curnt_l0s_rcv_state', 'rcv', 'rcvr', 'rcvr_l0s_goto_rcvry', 'received', 'sequence', 'smlh_eidle_inferred', 'state', 'timeout', 'timeout_nfts', 'training', 'when']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #267  行号: 7618-7623
  写入信号: ['next_lts_state_d']
  依赖信号: ['TP', 'next_lts_state']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #268  行号: 7626-7626
  写入信号: 无
  依赖信号: 无
  所有写入信号单驱动: False
  推荐解耦等级: 低（无寄存器写入，或仅组合逻辑）
------------------------------------------------------------
Always块 #269  行号: 7633-7654
  写入信号: ['curnt_l0s_xmt_state', 'next_ltssm_powerdown']
  依赖信号: ['EPX16_P0', 'EPX16_P0S', 'EPX16_P1', 'EPX16_P2', 'S_L0S_XMT_IDLE', 'S_L0S_XMT_WAIT', 'curnt_l0s_xmt_state', 'ltssm_powerdown']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #270  行号: 7657-7662
  写入信号: ['ltssm_powerdown']
  依赖信号: ['EPX16_P1', 'TP', 'next_ltssm_powerdown']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #271  行号: 7665-7683
  写入信号: ['lts_state', 'lts_state_d', 'smlh_in_l0', 'smlh_in_l0_l0s', 'smlh_in_l0s', 'smlh_in_l1', 'smlh_in_l23', 'smlh_in_rcvryspeed_predetect', 'smlh_in_rl0s']
  依赖信号: ['S_L0', 'S_L0S_RCV_ENTRY', 'S_L0S_XMT_EIDLE', 'S_L0S_XMT_ENTRY', 'S_L1_IDLE', 'S_LPBK_ACTIVE', 'TP', 'b0', 'curnt_l0s_xmt_state', 'lpbk_master', 'lts_state', 'lts_state_d', 'r_curnt_l0s_rcv_state', 'smlh_eq_pending_d']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #272  行号: 7694-7704
  写入信号: ['lts_state', 'smlh_l123_eidle_timeout']
  依赖信号: ['S_L123_SEND_EIDLE', 'S_L1_IDLE', 'S_L2_IDLE', 'TP', 'b0', 'clear', 'lts_state', 'smlh_l123_eidle_timeout', 'timeout_2ms']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #273  行号: 7713-7721
  写入信号: ['lpbk_active_entered_unaligned', 'lts_state', 'next_lts_state', 'rmlh_all_sym_locked']
  依赖信号: ['S_LPBK_ACTIVE', 'S_LPBK_ENTRY', 'TP', 'clear', 'lpbk_active_entered_unaligned', 'lts_state', 'next_lts_state', 'rmlh_all_sym_locked']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #274  行号: 7725-7731
  写入信号: ['ltssm_blockaligncontrol']
  依赖信号: ['TP', 'int_ltssm_blockaligncontrol']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #275  行号: 7738-7740
  写入信号: ['r_ltssm_rcvr_err_rpt_en']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #276  行号: 7755-7760
  写入信号: ['ltssm', 'ltssm_rcvr_err_rpt_en']
  依赖信号: ['S_RCVRY_LOCK', 'b0', 'ltssm_rcvr_err_rpt_en', 'r_ltssm_rcvr_err_rpt_en']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #277  行号: 7767-7769
  写入信号: ['smlh_lnknum_match_dis']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #278  行号: 7806-7820
  写入信号: ['lts_state', 'smlh_rx_rcvry_req']
  依赖信号: ['OS', 'S_L0', 'TP', 'TS', 'b0', 'b1', 'latched_rcvd_2_unexpect_ts', 'received', 'smlh_rx_rcvry_req']
  所有写入信号单驱动: False
  推荐解耦等级: 中（部分信号多驱动，需人工复核）
------------------------------------------------------------
Always块 #279  行号: 7823-7825
  写入信号: ['smlh_timeout_nfts']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #280  行号: 7833-7835
  写入信号: ['smlh_l0_to_recovery']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #281  行号: 7842-7844
  写入信号: ['smlh_l1_to_recovery']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #282  行号: 7858-7860
  写入信号: ['smlh_spd_change']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #283  行号: 7870-7872
  写入信号: ['int_smlh_link_mode_d']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
Always块 #284  行号: 7877-7879
  写入信号: ['smlh_lwd_change']
  依赖信号: ['TP']
  所有写入信号单驱动: True
  推荐解耦等级: 高（可安全解耦）
------------------------------------------------------------
