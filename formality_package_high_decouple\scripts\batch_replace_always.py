#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量替换Verilog代码中的always块为submodule实例化的脚本
读取submodules文件夹中的实例化文件，替换原始代码中对应的always块
作者：AI助手
"""
import os
import sys
import re
import argparse
import shutil
from collections import defaultdict

def read_file_lines(filename):
    """读取文件所有行"""
    with open(filename, 'r', encoding='utf-8') as f:
        return f.readlines()

def write_file_lines(filename, lines):
    """写入文件所有行"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.writelines(lines)

def extract_always_blocks_with_positions(lines):
    """提取所有always块及其位置信息"""
    always_blocks = []
    i = 0
    while i < len(lines):
        line = lines[i]
        if re.match(r'^\s*always\b', line) and not re.match(r'^\s*//', line):
            start_line = i
            if 'begin' in line:
                # begin-end块
                begin_count = line.count('begin')
                end_count = line.count('end')
                j = i + 1
                while j < len(lines) and begin_count > end_count:
                    begin_count += lines[j].count('begin')
                    end_count += lines[j].count('end')
                    j += 1
                end_line = j - 1
            else:
                # 单行always，找到分号结束
                j = i
                while j < len(lines) and ';' not in lines[j]:
                    j += 1
                end_line = j
            
            always_blocks.append({
                'start': start_line,
                'end': end_line,
                'lines': lines[start_line:end_line+1]
            })
            i = end_line + 1
        else:
            i += 1
    return always_blocks

def load_instantiation_files(submodules_dir):
    """加载所有实例化文件"""
    instantiations = {}
    
    if not os.path.exists(submodules_dir):
        print(f"错误: 子模块目录 {submodules_dir} 不存在")
        return instantiations
    
    for filename in os.listdir(submodules_dir):
        if filename.endswith('_instantiation.txt'):
            submod_name = filename.replace('_instantiation.txt', '')
            filepath = os.path.join(submodules_dir, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    instantiations[submod_name] = content
                    print(f"加载实例化文件: {filename}")
            except Exception as e:
                print(f"警告: 无法读取文件 {filename}: {e}")
    
    return instantiations

def extract_written_signals(block_lines):
    """提取always块写入的信号"""
    text = ''.join(block_lines)
    lefts = re.findall(r'([a-zA-Z_][\w\[\]]*)\s*(<=|=)', text)
    outputs = set([l[0].split('[')[0] for l in lefts])
    return outputs

def match_always_block_to_submodule(always_block, instantiations, log_blocks=None, high_decouple_only=False):
    """匹配always块到对应的子模块"""
    writes = extract_written_signals(always_block['lines'])

    # 如果只替换高解耦等级的模块，先检查解耦等级
    if high_decouple_only and log_blocks:
        line_range = f"{always_block['start']+1}-{always_block['end']+1}"
        if line_range in log_blocks:
            level = log_blocks[line_range]['level']
            if '高（可安全解耦）' not in level:
                return None, None  # 不是高解耦等级，跳过
        else:
            return None, None  # 没有分析结果，跳过

    # 尝试根据写入信号匹配子模块
    for submod_name, inst_code in instantiations.items():
        # 方法1: 根据信号名匹配
        if len(writes) == 1:
            signal_name = list(writes)[0]
            if signal_name + '_sub' == submod_name:
                return submod_name, inst_code

        # 方法2: 检查实例化代码中是否包含写入信号
        for signal in writes:
            if f'.{signal}({signal})' in inst_code:
                return submod_name, inst_code

    return None, None

def replace_always_blocks(lines, instantiations, log_blocks=None, high_decouple_only=False):
    """替换always块为实例化代码"""
    # 提取always块信息
    always_blocks = extract_always_blocks_with_positions(lines)

    if not always_blocks:
        print("未找到always块")
        return lines, 0

    print(f"找到 {len(always_blocks)} 个always块")
    if high_decouple_only:
        print("只替换解耦等级高的always块")

    # 从后往前替换，避免行号变化影响
    replaced_count = 0
    skipped_count = 0
    new_lines = lines.copy()

    for block in reversed(always_blocks):
        submod_name, inst_code = match_always_block_to_submodule(block, instantiations, log_blocks, high_decouple_only)

        if submod_name and inst_code:
            print(f"替换always块 (行号: {block['start']+1}-{block['end']+1}) -> {submod_name}")

            # 替换always块为实例化代码
            inst_lines = [line + '\n' for line in inst_code.split('\n') if line.strip()]

            # 保持原有缩进
            original_indent = ''
            if block['lines']:
                match = re.match(r'^(\s*)', block['lines'][0])
                if match:
                    original_indent = match.group(1)

            # 为实例化代码添加缩进
            indented_inst_lines = []
            for line in inst_lines:
                if line.strip():
                    indented_inst_lines.append(original_indent + line)
                else:
                    indented_inst_lines.append(line)

            # 执行替换
            new_lines[block['start']:block['end']+1] = indented_inst_lines
            replaced_count += 1
        else:
            writes = extract_written_signals(block['lines'])
            reason = "未知原因"
            if log_blocks:
                reason = analyze_unmatchable_reason(block['start'], block['end'], writes, log_blocks)

            if high_decouple_only and log_blocks:
                line_range = f"{block['start']+1}-{block['end']+1}"
                if line_range in log_blocks:
                    level = log_blocks[line_range]['level']
                    if '高（可安全解耦）' not in level:
                        skipped_count += 1
                        continue  # 跳过非高解耦等级的块，不显示警告

            print(f"警告: 无法找到匹配的子模块 (行号: {block['start']+1}-{block['end']+1}, 写入信号: {writes})")
            print(f"  原因: {reason}")

    if high_decouple_only:
        print(f"跳过了 {skipped_count} 个非高解耦等级的always块")

    return new_lines, replaced_count

def load_log_analysis(log_file='log.txt'):
    """加载log.txt中的always块分析结果"""
    log_blocks = {}
    
    if not os.path.exists(log_file):
        print(f"警告: 未找到{log_file}文件，无法分析解耦度")
        return log_blocks
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        print(f"警告: 无法读取{log_file}文件")
        return log_blocks
    
    # 解析always块信息
    pattern = r'Always块 #(\d+)\s+行号: (\d+)-(\d+).*?写入信号: ([^\n]+).*?推荐解耦等级: ([^\n]+)'
    matches = re.findall(pattern, content, re.DOTALL)
    
    for match in matches:
        block_num, start, end, writes, level = match
        line_range = f"{start}-{end}"
        log_blocks[line_range] = {
            'block_num': int(block_num),
            'start': int(start),
            'end': int(end),
            'writes': writes.strip(),
            'level': level.strip()
        }
    
    return log_blocks

def analyze_unmatchable_reason(block_start, block_end, writes, log_blocks):
    """分析无法匹配的原因"""
    line_range = f"{block_start+1}-{block_end+1}"
    
    if line_range in log_blocks:
        log_info = log_blocks[line_range]
        level = log_info['level']
        
        if '高（可安全解耦）' in level:
            return f"解耦度高但无匹配文件 - 可能子模块生成失败或文件缺失"
        elif '中（部分信号多驱动，需人工复核）' in level:
            return f"解耦度中等 - {level}，未自动生成子模块"
        elif '低（无寄存器写入，或仅组合逻辑）' in level:
            return f"解耦度低 - {level}，不适合独立为子模块"
        else:
            return f"解耦等级: {level}"
    else:
        return "未在log.txt中找到对应分析结果，可能是新增的always块"

def main():
    parser = argparse.ArgumentParser(description='批量替换Verilog代码中的always块为submodule实例化')
    parser.add_argument('input_file', help='输入的Verilog文件')
    parser.add_argument('--submodules-dir', default='./submodules', help='子模块目录')
    parser.add_argument('--output-file', help='输出文件名 (默认为输入文件名_replaced.sv)')
    parser.add_argument('--backup', action='store_true', help='备份原文件')
    parser.add_argument('--high-decouple-only', action='store_true', help='只替换解耦等级高的always块')
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 {args.input_file} 不存在")
        sys.exit(1)
    
    # 确定输出文件名
    if args.output_file:
        output_file = args.output_file
    else:
        base_name = os.path.splitext(args.input_file)[0]
        ext = os.path.splitext(args.input_file)[1]
        if args.high_decouple_only:
            output_file = f"{base_name}_high_decouple_only{ext}"
        else:
            output_file = f"{base_name}_replaced{ext}"
    
    # 备份原文件
    if args.backup:
        backup_file = args.input_file + '.backup'
        shutil.copy2(args.input_file, backup_file)
        print(f"已备份原文件到: {backup_file}")
    
    print(f"输入文件: {args.input_file}")
    print(f"输出文件: {output_file}")
    print(f"子模块目录: {args.submodules_dir}")
    print("="*60)
    
    # 读取原始文件
    try:
        lines = read_file_lines(args.input_file)
        print(f"读取原始文件成功，共 {len(lines)} 行")
    except Exception as e:
        print(f"错误: 无法读取输入文件: {e}")
        sys.exit(1)
    
    # 加载实例化文件
    instantiations = load_instantiation_files(args.submodules_dir)
    if not instantiations:
        print("错误: 未找到任何实例化文件")
        sys.exit(1)
    
    # 加载log.txt分析结果
    log_blocks = load_log_analysis()
    if log_blocks:
        print(f"加载了log.txt中 {len(log_blocks)} 个always块的分析结果")
    
    print(f"加载了 {len(instantiations)} 个实例化文件")
    print("="*60)
    
    # 执行替换
    new_lines, replaced_count = replace_always_blocks(lines, instantiations, log_blocks, args.high_decouple_only)
    
    # 写入输出文件
    try:
        write_file_lines(output_file, new_lines)
        print("="*60)
        print(f"替换完成: {replaced_count} 个always块被成功替换")
        print(f"输出文件: {output_file}")
    except Exception as e:
        print(f"错误: 无法写入输出文件: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()


