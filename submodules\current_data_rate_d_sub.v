module current_data_rate_d_sub (
    input wire core_rst_n,
    input wire TP,
    input wire current_data_rate,
    output reg [N-1:0] current_data_rate_d // TODO: 请根据实际宽度修改
);

    always @(posedge core_clk or negedge core_rst_n) begin : current_data_rate_d_PROC
        if ( ~core_rst_n )
            current_data_rate_d <= #TP 0;
        else
            current_data_rate_d <= #TP current_data_rate;
    end //always


endmodule
