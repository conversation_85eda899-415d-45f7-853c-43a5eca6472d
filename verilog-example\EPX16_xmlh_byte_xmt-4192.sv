// ------------------------------------------------------------------------------
// 
// Copyright 2002 - 2023 Synopsys, INC.
// 
// This Synopsys IP and all associated documentation are proprietary to
// Synopsys, Inc. and may only be used pursuant to the terms and conditions of a
// written license agreement with Synopsys, Inc. All other use, reproduction,
// modification, or distribution of the Synopsys IP or the associated
// documentation is strictly prohibited.
// Inclusivity & Diversity - Read the Synopsys Statement on Inclusivity and Diversity at.
// https://solvnetplus.synopsys.com/s/article/Synopsys-Statement-on-Inclusivity-and-Diversity
// 
// Component Name   : DWC_pcie_ctl
// Component Version: 6.00a-lu08
// Release Type     : LU
// Build ID         : *******.PCIeParseConfig_1.PCIeSimulate_1.PCIeTbCommon_3.SNPSPHYSetup_1
// ------------------------------------------------------------------------------

// -------------------------------------------------------------------------
// ---  RCS information:
// ---    $DateTime: 2023/03/13 23:15:13 $
// ---    $Revision: #4 $
// ---    $Id: //dwh/pcie_iip/main_600a_lu/fairbanks/design/Layer1/xmlh_byte_xmt.sv#4 $
// -------------------------------------------------------------------------
// --- Module Description: Transmit MAC Layer handler Byte Transmitter
// -----------------------------------------------------------------------------
// --- Terminology
// --- chunk: The amount of data that can be transmitted by all active lanes in one clock cycle
// --- slice: The amount of data that can be transmitted by one lane in one clock cycle
// -----------------------------------------------------------------------------

`include "include/EPX16_DWC_pcie_ctl_all_defs.svh"

 
 module EPX16_xmlh_byte_xmt (
    core_clk,
    core_rst_n,

    app_sris_mode_i,
    ltssm_cxl_sh_bypass, // SyncHeader_Bypass
    cxl_mode_enable,
    cfg_fast_link_mode,
    ltssm_clear,
    smlh_ltssm_state,
    smlh_ltssm_last,
    ltssm_lpbk_slave_lut,
    cfg_n_fts,
    cfg_skip_interval,
    cfg_ext_synch,
    cfg_mod_ts,
    smlh_link_mode,
    ltssm_cmd_i,
    ltssm_ts_cnt_en_i,
    ltssm_ts_cnt_rst,
    ltssm_xlinknum,
    ltssm_xk237_4lannum,
    ltssm_xk237_4lnknum,
    ltssm_ts_cntrl,
    ltssm_mod_ts,
    ltssm_ts_alt_protocol,
    ltssm_ts_alt_prot_info,
    ltssm_ts_auto_change,
    ltssm_in_lpbk,
    ltssm_lanes_active,
    lpbk_eq_lanes_active,
    ltssm_in_lpbk_active,
    cfg_lpbk_en,
    cfg_prbs_en,
    ltssm_gen4_compliance_jmp,
    smlh_no_turnoff_lanes,
    ltssm_eidle_cnt,
    latched_ts_nfts,

    xdlh_xmlh_stp_i,
    xdlh_xmlh_sdp_i,
    xdlh_xmlh_eot_i,
    next_xdlh_xmlh_eot_i,
    next_xdlh_xmlh_stp_i,
    next_xdlh_xmlh_sdp_i,
    xdlh_xmlh_pad_i,
    xdlh_xmlh_data_i,
    xdlh_xmlh_pkt_pending_i,
    ltssm_in_lpbkentry,
    ltssm_cmd_send_eieos_for_pset_map,
    ltssm_usp_eq_redo,
    ltssm_in_compliance,

    active_nb,
    current_data_rate,
    ltssm_ts_data_rate,
    ltssm_ts_speed_change,
    smlh_mcs_rcvd,
    rpipe_rxerror,
    cfg_compliance_sos,
    latched_ts_data_rate,
    scramble_lfsr_value,
    scramble_lfsr_rollover,
    scramble_even_parity,
    eqctl_eqts_info,
    eqctl_mask_eieos,
    eqctl_2mask_eieos_pulse,
    lpbk_master,
    cfg_upstream_port,
    eqctl_pset_ltx,
    smlh_gen3_error_count_i,
    cfg_margin_control,
    smlh_ctlskp_info,
    ltssm_in_rcvry_cfg_idle,
    ltssm_in_rcvry_cfg_idle_g3,
    cdm_ras_des_eidle_cnt_sel,
    xdlh_xmlh_pid,
    // ---------------------------------outputs ------------------------
    xmtbyte_ts_pcnt,
    xmtbyte_ts_data_diff,
    xmtbyte_1024_ts_sent,
    xmtbyte_spd_chg_sent,
    xmtbyte_dis_link_sent,
    xmtbyte_loe_2_ts1_ec_00b_sent,
    xmtbyte_txdata_dv,
    xmtbyte_txdata,
    xmtbyte_txdatak,
    xmtbyte_txstartblock,
    xmtbyte_txelecidle,
    xmtbyte_g5_lpbk_deassert,
    xmtbyte_txdetectrx_loopback,

    xmtbyte_xdlh_halt_o,
    xmlh_skip_pending,
    xmtbyte_cmd_is_data,

    xmtbyte_ts1_sent,
    xmtbyte_ts2_sent,
    xmtbyte_fts_sent,
    xmtbyte_idle_sent,
    xmtbyte_eidle_sent,
    xmtbyte_eies_sent,
    xmtbyte_skip_sent_o,
    xmtbyte_skip_insert,
    xmtbyte_ctlskip_sent,
    xmtbyte_link_in_training,
    xmlh_rst_flit_alignment,
    xmtbyte_txcompliance
);
parameter INST          = 0;                        // The uniquifying parameter for each port logic instance.
parameter NL            = `EPX16_CX_NL;                   // Max number of lanes supported
parameter NB            = `EPX16_CX_NB;                   // Number of symbols (bytes) per clock cycle
parameter NBK           = `EPX16_CX_NBK;                  // Number of symbols (bytes) per clock cycle for datak
parameter NW            = `EPX16_CX_PL_NW;                // Number of 32-bit dwords handled by the datapath each clock.
parameter DW            = `EPX16_CX_PL_DW;                // Width of datapath in bits.
parameter NBIT          = `EPX16_CX_NB * 8;               // Number of bits per lane
parameter NBIT2         = (NBIT> 128) ? 128 : NBIT; // Number of bits per lane for 32s logic
parameter NBYTE         = `EPX16_CX_PL_NW * 4;            // The number of bytes in CX_DW
parameter LOG2NBYTE     = `EPX16_CX_LOGBASE2(NBYTE);      // log2 number of bytes in CX_DW
parameter TP            = `EPX16_TP;                      // Clock to Q delay (simulator insurance)
parameter TIMER_BITS    = 11;
parameter TX_PSET_WD    = 4;                        // preset bit width
parameter EIEOS_MASK_LOGIC_ENABLE = 0;              // set to 1 if you want to go back to per lane mechanism
parameter TS1_NUM = `EPX16_GEN3_TS1_NUM_FOR_RESET_EIEOS;  // <65535 for test only. real is default 65535
parameter AW            = `EPX16_CX_ANB_WD;               // Width of the active number of bytes
parameter TS_0_WD       = 48;                       // ts0 info sym6-1

localparam EQTS_WD      = 32;                       // Number of bits of Per Lane Equalization Information
localparam SLICE_WD     = (NW>8) ? LOG2NBYTE : 5;   // scale for 512b and higher widths, hardcoded for lower widths for backward compatibility
localparam NB_G1        = `EPX16_CX_MAC_SMODE_GEN1;       // Gen1 Sness
localparam NB_G3        = `EPX16_CX_MAC_SMODE_GEN3;       // Gen3 Sness
localparam SLICEK_WD    = LOG2NBYTE;                // for k-char to fix lint
localparam S_WD         = `EPX16_CX_LOGBASE2(16/NB_G1);   // ts_sym_cnt bit width to fix lint

localparam PAT_NB       = (NB==32) ? 16 : NB ;
localparam PAT_NBK      = (NB==32) ? 16 : NBK;
localparam PAT_NBIT     = PAT_NB * 8;
// NW=16
localparam MAX_NL_NB4   = 16;                       // Max Number of lanes @active_nb=4
localparam MAX_NL_NB8   = 8;                        // Max Number of lanes @active_nb=8
localparam MAX_NL_NB16  = 4;                        // Max Number of lanes @active_nb=16
localparam CMD_NORM_WD  =                           // Number of bits of ltssm_cmd_is_norm signal
                            3
                          ;                       // Number of bits of ltssm_cmd_is_norm signal
localparam TXSB_WD      = `EPX16_CX_MAC_SB_WD;          // Number of bits of per txstartblock
localparam TXSH_WD      = `EPX16_CX_MAC_SH_WD;          // Number of bits of per txsyncheader
localparam OS_WD      = `EPX16_CX_MAC_SB_WD; // Number of OS width


// =============================================================================
// State Parameters
// =============================================================================
parameter /* VCS enum enum_xbyte_curnt_state */
          S_IDLE                =   4'h0,
          S_EIDLE               =   4'h1,
          S_XMT_EIDLE           =   4'h2,
          S_FTS                 =   4'h3,
          S_TS                  =   4'h4,
          S_SKIP                =   4'h5,
          S_CMP                 =   4'h6,
          S_XPKT_WAIT4_START    =   4'h7,
          S_EIES                =   4'h9,
          S_EIES_SYM            =   4'hA,
          S_SDS                 =   4'hB,
          S_EDS                 =   4'hC,
          S_XPKT_WAIT4_STOP     =   4'h8;

// =============================================================================
// input IO Declarations
// =============================================================================
input                   core_clk;
input                   core_rst_n;

input                   app_sris_mode_i;            // application signal to enable the SRIS mode
input                   ltssm_cxl_sh_bypass;        // SyncHeader_Bypass
input                   cxl_mode_enable;            // Indicates whether the link should operate in CXL or PCIe mode
input                   cfg_fast_link_mode;
input                   ltssm_clear;                // used to clear persistency count when a ltssm state transition
input   [5:0]           smlh_ltssm_state;           // for debug
input   [5:0]           smlh_ltssm_last;            // last ltssm state
input   [NL-1:0]        ltssm_lpbk_slave_lut;       // lane under test for lpbk slave
input   [7:0]           cfg_n_fts;                  // 8bits bus to specify the number of FTS we wish to receive for our receiver
input   [10:0]          cfg_skip_interval;          // the number of symbol times between skip requests
input                   cfg_ext_synch;              // Extended synch for N_FTS configured in CDM
input                   cfg_mod_ts;                 // modified ts format
input   [5:0]           smlh_link_mode;             // 10000 -- x16, 01000 -- x8, 00100 -- x4, 00010 -- x2, 00001 -- x1
input   [3:0]           ltssm_cmd_i;                // encoded command to notify transmitter of the proper action based on LTSSM states
input                   ltssm_ts_cnt_en_i;          // enable TS counting logic to transmit an EIEOS every 32 or 65536 TSs
input                   ltssm_ts_cnt_rst;           // reset TS1/TS2 count
input   [7:0]           ltssm_xlinknum;             // 8bits indicate link number to be inserted in training sequence
input   [NL-1:0]        ltssm_xk237_4lnknum;        // 1 bit per lane, 1 -- K237, 0 -- send lane number
input   [NL-1:0]        ltssm_xk237_4lannum;        // 1 bit per lane, 1 -- K237, 0 -- send link number
input   [7:0]           ltssm_ts_cntrl;             // training sequence control
input                   ltssm_mod_ts;               // Tx modified TS
input                   ltssm_ts_alt_protocol;      // Alternate protocol
input   [55:0]          ltssm_ts_alt_prot_info;     // sym14-8 for AP
input                   ltssm_ts_auto_change;       // autonomous change/upconfig capable/select de-emphasis bit.  bit 6 of the data rate identifier field.
input   [7:0]           latched_ts_nfts;            // latched number of fast training sequence number from TS ordered set of receiver
input                   ltssm_in_lpbk;              // LTSSM in Loopback.Active state
input   [2:0]           ltssm_eidle_cnt;            // 4 bits, indicates how many EIOS sets to send before returning xmtbyte_eidle_sent.  0=1 EIOS, 1=2 EIOS, etc.
input   [NL-1 :0]       ltssm_lanes_active;         // LTSSM latched lanes that are active based on the link negotiation
input   [NL-1:0]        lpbk_eq_lanes_active;       // One active lane from the cfg_lane_under_test
input                   ltssm_in_lpbk_active;              // LTSSM in Loopback.Active state
input                   cfg_lpbk_en;
input                   cfg_prbs_en;
input   [3:0]           ltssm_gen4_compliance_jmp;  // Gen4 compliance setting# for Jitter Measurment Pattern
input   [NL-1 :0]       smlh_no_turnoff_lanes;      // No turnoff lanes

input   [NW-1 :0]       xdlh_xmlh_stp_i;            // 1 bit per dword, indicates which dword is stp
input   [NW-1 :0]       xdlh_xmlh_sdp_i;            // 1 bit per dword, indicates which dword is sdp
input   [NW-1 :0]       xdlh_xmlh_eot_i;            // 1 bit per dword, indicates which dword is eot
input   [NW-1 :0]       next_xdlh_xmlh_eot_i;       // 1 bit per dword, indicates which dword is eot
input   [NW-1 :0]       xdlh_xmlh_pad_i;            // 1 bit per dword, indicates which dword is pad
input   [NW-1 :0]       next_xdlh_xmlh_stp_i;
input   [NW-1 :0]       next_xdlh_xmlh_sdp_i;
input   [DW-1:0]        xdlh_xmlh_data_i;           // Data from the Data Link Layer to transmit
input                   xdlh_xmlh_pkt_pending_i;    // XDLH has registered packet pending transmission
input                   ltssm_in_lpbkentry;         // LTSSM in Loopback.Entry state
input                   ltssm_cmd_send_eieos_for_pset_map; // send eieos continuously for pset-coef mapping
input                   ltssm_usp_eq_redo;          // eq redo for usp
input                   ltssm_in_compliance;        // LTSSM in polling.compliance state

input   [2:0]           current_data_rate;          // 2'b00=running at gen1 speeds, 2'b01=running at gen2 speeds, 2'b10=running at gen3 speeds, 2'b11=running at gen4 speeds
input   [4:0]           ltssm_ts_data_rate;         // Gen2 Data rate to advertise support for in the ts.  bits 4:1 of the data rate identifier field.
input                   ltssm_ts_speed_change;      // Value to put in the speed change bit of TS ordered sets
input  [NL-1:0]         smlh_mcs_rcvd;              // detected Modified Compliance Sequence
input  [NL-1:0]         rpipe_rxerror;              // when asserted, RMLH indicates that the receiver has detected an rx disparity or code error
input                   cfg_compliance_sos;         // Send skips during compliance
input  [4:0]            latched_ts_data_rate;       // latched TS sequence data rate from partner
input   [(23*NL)-1:0]   scramble_lfsr_value;        // LFSR value from Scrambler. Used for SKP OS
input   [NL-1:0]        scramble_lfsr_rollover;     // LFSR value matches seed. Used for inserting EIEOS after a sequence of 65536 uninterrupted TS1s
input   [NL-1:0]        scramble_even_parity;       // Data Even Parity from Scrambler. Used for SKP OS
input   [NL*EQTS_WD-1:0]eqctl_eqts_info;            // Equalization Information for Symbols 6-9 of EQ TSs and Gen3 TSs
input   [NL-1:0]        eqctl_mask_eieos;           // Mask EIEOS. Replaces EIEOS OS Identifier with TS1 OS Identifier
input   [NL-1:0]        eqctl_2mask_eieos_pulse;    // 2 consecutive EC TS1 rcvd pulse with a request to reset EIEOS count
input                   lpbk_master;                // indicates that the ltssm is the loopback master
input   [AW-1:0]        active_nb;                  // active number of symbols per lane per clock. bit0=1s, bit1=2s, bit2=4s, bit3=8s , bit4=16s
input                   cfg_upstream_port;
input   [NL*TX_PSET_WD-1:0] eqctl_pset_ltx;         // Tx preset being used per lane
input   [NL*8-1:0]      smlh_gen3_error_count_i;    // per lane version of gen3 Compliance error count
input  [NL*16-1:0]      cfg_margin_control;         // Margining Control [2:0] Receiver Number[5:3]Margin Type[6]Usage Molde[7]Rsvd[15:8]Margin Payload
input  [NL*16-1:0]      smlh_ctlskp_info;           // Margining Status  [2:0] Receiver Number[5:3]Margin Type[6]Usage Molde[7]Rsvd[15:8]Margin Payload
input                   ltssm_in_rcvry_cfg_idle;    // LTSSM in Recovery.Idle or Configuration.Idle state
input                   ltssm_in_rcvry_cfg_idle_g3; // LTSSM in Recovery.Idle or Configuration.Idle state

input   [1:0]           cdm_ras_des_eidle_cnt_sel;  // Number of Transmit E-IDLE ordered set for Ras D.E.S Silicon Debug
input   [NW-1:0]        xdlh_xmlh_pid;               // PID location per DW on xdlh_xmlh_data[511:0]

// =============================================================================
// output IO Declarations
// =============================================================================
output  [10:0]          xmtbyte_ts_pcnt;            // transmit ts persistency count
output                  xmtbyte_ts_data_diff;       // current ts data different from previous
output                  xmtbyte_1024_ts_sent;       // 1024 ts sent in a state
output                  xmtbyte_spd_chg_sent;       // speed_change bit sent
output                  xmtbyte_dis_link_sent;      // disable link bit set in Tx TS
output                  xmtbyte_loe_2_ts1_ec_00b_sent; //less or equal 2 ts1 sent with EC==00b
output                  xmtbyte_txdata_dv;          // Data Valid, gen1/gen2: always 1, gen3: 0 for one cycle every 4*active_nb blocks
output  [(NB*8*NL)-1:0] xmtbyte_txdata;             // Transmit data to the phy
output  [(NBK*NL)-1:0]  xmtbyte_txdatak;            // Indicates which bytes are k characters.  1 bit per byte of xmtbyte_txdata
output  [(NL*TXSB_WD)-1:0] xmtbyte_txstartblock;       // Indicates the starting symbol for a 130b block

output  [OS_WD-1:0]     xmtbyte_ts1_sent;           // Indicates 1 TS1 ordered set has been sent based on LTSSM's command
output  [OS_WD-1:0]     xmtbyte_ts2_sent;           // Indicates 1 TS2 ordered set has been sent based on LTSSM's command
output                  xmtbyte_fts_sent;           // Indicates all fast training sequences have been sent based on LTSSM's command
output                  xmtbyte_idle_sent;          // Indicates 1 idle has been sent based on LTSSM's command
output                  xmtbyte_eidle_sent;         // Indicates 1 eidle ordered set has been sent based on LTSSM's command
output                  xmtbyte_eies_sent;          // Indicates 1 EIEOS Ordered Set has been sent
output                  xmtbyte_skip_sent_o;        // Indicates 1 skip ordered set has been sent based on LTSSM's command
output                  xmtbyte_skip_insert;        // scheduled a skp
output                  xmtbyte_ctlskip_sent;       // Indicates 1 enhanced skip ordered set has been sent based on LTSSM's command
output                  xmtbyte_xdlh_halt_o;        // Halt the flow of data from the xdlh
output                  xmlh_skip_pending;          // XMLH Skip pending to XDLH TLP GEN
output                  xmtbyte_cmd_is_data;        // xmtbyte state machine is waiting for packet start or packet end
output                  xmtbyte_link_in_training;   // Indicates byte machine is sending training sequences or compliance pattern

output  [NL-1:0]        xmtbyte_txelecidle;         // Lanes to put in electrical idle
output                  xmtbyte_g5_lpbk_deassert;   // deassert loopback signal at gen5 rate in LPBK_EXIT_TIMEOUT
output                  xmtbyte_txdetectrx_loopback;// Indicates detectrx or loopback mode
output  [NL-1:0]        xmtbyte_txcompliance;       // Set negative running disparity (for compliance pattern)
output                  xmlh_rst_flit_alignment;

// =============================================================================
// Regs & Wires
// =============================================================================
// Register outputs
wire    [3:0]           ltssm_cmd;
wire                    pid_rcvd_pulse;
wire                    skp_win;
reg                     skp_win_d;
wire                    eie_win;
wire                    eid_win;
wire                    eie_win_i;
wire                    eid_win_i;
reg                     eie_win_d;
reg                     eid_win_d;
reg  [11:0]             pid_count;   //count PID
reg  [11:0]             pid_count_i; //count PID
wire                    xmlh_rst_flit_alignment;
wire                    xmt_cnt_130b_is_64;             // 128b/130b Transmit Counter

wire   l0p_sris_mode_enabled;
wire   app_sris_mode;
assign app_sris_mode = app_sris_mode_i;
assign xmtbyte_l0p_sris_mode_enabled = app_sris_mode;

reg                     data_stream_in_progress;        // Data Stream in Progress
wire                    int_mux_pkt_pending;
assign int_mux_pkt_pending = xdlh_xmlh_pkt_pending_i;

reg                     xdlh_xmlh_pkt_pending;
always @* begin : xdlh_xmlh_pkt_pending_not_cxl_PROC
    xdlh_xmlh_pkt_pending = 0;
    xdlh_xmlh_pkt_pending = int_mux_pkt_pending;
end // xdlh_xmlh_pkt_pending_not_cxl_PROC
reg     [SLICE_WD:0]    data_cycles;                    // the number of cycles needed to send one CX_DW of data with
                                                        // the current active_nb and active_lanes
wire    [AW-3:0]        active_nb_shift;                // Used to shift certain values according to S-ness. 1s = 0, 2s = 1, 4s = 2, 8s = 3 , 16s = 4
wire    [AW-3:0]        active_nb_shift_os;             // Used to shift certain values according to S-ness. 1s = 0, 2s = 1, 4s = 2, 8s = 3 , 16s = 4
reg     [5:0]           int_link_mode;                  // 10000 -- x16, 01000 -- x8, 00100 -- x4, 00010 -- x2, 00001 -- x1
wire    [5:0]           int_active_nb;                  // Internal active_nb
reg                     null_ieds_for_skip_sent_d;
wire                    null_ieds_for_skip_sent;
wire                    null_ieds_en;
wire                    ds_null_ieds; // downsized linkwidth null ieds
wire                    cxl_ieds_pat_en;
wire                    cxl_x1_nw_gtr_8;   // 
wire                    cxl_x1_sris;   // 
wire                    cxl_x1_no_sh_bp;   // 
wire                    xmtbyte_xdlh_halt;
wire                    xmtbyte_xdlh_halt_o;
reg                     xmtbyte_xdlh_halt_d;
reg                     flit_null_d_r;
wire                    latched_flit_null_d;
wire                    pid_rcvd   = |xdlh_xmlh_pid & ~xmtbyte_xdlh_halt;
wire                    pid_rcvd_d = |xdlh_xmlh_pid & ~xmtbyte_xdlh_halt_d;
wire                    flit_null_i;
wire                    flit_null_d;
wire                    cmd_is_data;                    // The current cmd is data
wire                    eds_on;                         // convert the whole null flit to iEDS for cxl including narrow linkwidth
wire                    eds_inserted;                   // Indicates EDS token has been inserted into data block
reg                     cxl_x1_sris_null_for_ieds;      // detect x1 null flit for ieds
wire                    cxl_x1_sris_null_ieds_halt_n;
reg                     cxl_x1_no_sh_bp_null_for_ieds;    // detect x1 null flit for ieds
wire                    cxl_x1_no_sh_bp_null_ieds_halt_n;
wire                    cxl_x1_nsris_nshb_pid4000_i;
wire                    cxl_x1_ns_nsb_p4000; // no syncheaderbypass, no sris, 8sx1, pid = 4000h
assign                  cxl_x1_nsris_nshb_pid4000_i = (cxl_mode_enable & active_nb == 8 & int_link_mode == 1 & ltssm_cxl_sh_bypass == 0 & xdlh_xmlh_pid == 16'h4000);
assign                  flit_null_i         = 1'b0;
assign                  flit_null_d         = 1'b0;
assign                  ds_null_ieds        = 1'b0;
assign                  null_ieds_en        = 1'b0;
assign                  cxl_x1_ns_nsb_p4000 = 1'b0;
assign                  cxl_x1_nw_gtr_8     = 1'b0;
assign                  cxl_x1_sris         = 1'b0;
assign                  cxl_x1_no_sh_bp     = 1'b0;

/* VCS state_vector xbyte_curnt_state enum enum_xbyte_curnt_state */
reg     [3:0]           xbyte_curnt_state;
reg     [3:0] /* VCS enum enum_xbyte_curnt_state */ xbyte_next_state;


wire    [NW-1 :0]       xdlh_xmlh_stp       = xdlh_xmlh_stp_i
; // 1 bit per dword, indicates which dword is stp
wire    [NW-1 :0]       xdlh_xmlh_sdp       = xdlh_xmlh_sdp_i; // 1 bit per dword, indicates which dword is sdp
wire    [NW-1 :0]       xdlh_xmlh_eot       =
 xdlh_xmlh_eot_i;     // 1 bit per dword, indicates which dword is eot, no eot for CXL because Layer2 sends null flits if no pkts
wire    [NW-1 :0]       next_xdlh_xmlh_eot  = next_xdlh_xmlh_eot_i; // 1 bit per dword, indicates which dword is eot
wire    [NW-1 :0]       xdlh_xmlh_pad       = xdlh_xmlh_pad_i;      // 1 bit per dword, indicates which dword is pad
wire    [NW-1 :0]       next_xdlh_xmlh_stp  = next_xdlh_xmlh_stp_i;
wire    [NW-1 :0]       next_xdlh_xmlh_sdp  = next_xdlh_xmlh_sdp_i;
wire                    ieds_on             = ltssm_cxl_sh_bypass ? eid_win | eie_win : eds_on & !skp_win;
wire    [DW-1:0]        xdlh_xmlh_data      = xdlh_xmlh_data_i;
reg     [(NB*8*NL)-1:0] int_xmtbyte_txdata;
reg     [(NBK*NL)-1:0]  int_xmtbyte_txdatak;
wire                    int_txdata_dv;
reg     [(NL*TXSB_WD)-1:0] int_xmtbyte_txstartblock;
reg     [CMD_NORM_WD:0] ltssm_cmd_is_norm;
wire    [TXSB_WD-1:0]   xmtbyte_txstartblock_i;
reg     [NL*8-1:0]      smlh_gen3_error_count_d;
wire    [NL*8-1:0]      smlh_gen3_error_count;

wire                    os_sent_d;
wire                    skip_os_sent_d;
wire    [OS_WD-1:0]     xmtbyte_ts1_sent;
reg                     ts1_sent_d;
wire    [OS_WD-1:0]     xmtbyte_ts2_sent;
wire                    xmtbyte_fts_sent;
reg                     xmtbyte_idle_sent;
wire    [OS_WD-1:0]     pre_xmtbyte_eidle_sent;
//wire                    xmtbyte_eidle_sent;
wire    [OS_WD-1:0]     pre_xmtbyte_skip_sent;
wire                    xmtbyte_skip_sent;
reg                     xmtbyte_skip_sent_d;
reg                     xmtbyte_link_in_training;
wire                    xmlh_pat_fts_sent;
wire                    ltssm_in_lpbk_i;
reg                     ltssm_in_lpbk_d;
wire                    ltssm_in_lpbk_g3;
reg                     xmtbyte_txdetectrx_loopback;
wire    [NL-1:0]        xmtbyte_txcompliance;
wire    [NL-1:0]        xmtbyte_txcompliance_g12;
wire    [NL-1:0]        xmtbyte_txcompliance_g3;
wire            xmlh_skip_pending;

// =============================================================================
// Internal signals
// =============================================================================
wire    [OS_WD-1:0]     xmlh_pat_eidle_sent;
reg     [NL-1:0]        xmtbyte_txcompliance_cmp_i;
wire    [NL-1:0]        xmtbyte_txcompliance_turnoff_i;
wire    [NL-1:0]        xmtbyte_txcompliance_i;
wire                    os_start;                       // Indicate the start of an ordered set
wire                    os_end;                         // Indicate the start of an ordered set
//reg                     xmtbyte_link_in_training_i;
reg     [3:0]           next_cmd;                       // the next command to execute

wire                    int_eidle_i;
reg                     int_eidle;
reg     [NL-1:0]        int_lanes_active;
wire    [NL-1:0]        int_lanes_active_for_data;
wire    [4:0]           active_lane_cnt;                // the number of active lanes 1=1 lane, 2=2 lanes, etc.
                                                        // Not used in 1s mode so it will be zero when 16 lanes are active

wire                    last_chunk;

reg    [TIMER_BITS-1:0] skip_timer;                     // Skip timer for 2.5 GT/s and 5 GT/s data rate
reg    [TIMER_BITS-1:0] skip_timer_130b;                // Skip timer for 8 GT/s data rate

wire                    pkt_in_progress;
wire                    pkt_in_progress_i;
reg                     latched_pkt_in_progress;
wire                    valid_pkt_in_progress;
wire                    valid_pkt_in_progress_i;
reg                     valid_latched_pkt_in_progress;
reg     [NW-1:0]        mask_sdp;

reg     [7:0]           cmp_dly_lane;                   // Select which lane gets delayed compliance pattern.  One hot for lanes 0-7, lanes above replicate 0-7
wire    [15:0]          int_cmp_dly_lane;               // Select which lane gets delayed compliance pattern.  One hot for lanes 0-7, lanes above replicate 0-7
reg     [(NL*7)-1:0]    err_cnt;                        // Per lane receive error count for compliance

reg     [NL-1:0]        pattern_lock;                   // Per lane compliance pattern lock
wire    [OS_WD-1:0]     pre_xmtbyte_eies_sent;
wire                    xmtbyte_eies_sent;
reg                     xmtbyte_eies_sent_d;
reg                     first_eieos_sent;
wire                    delay_load_pat;                 // Delay assertion of load_pat due to pending de-assertion of xmtbyte_txdata_dv
reg                     latched_load_pat;               // Latched version of load_pat when xmtbyte_txdata_dv is to be de-asserted
wire                    txdata_dv0_required;            // xmtbyte_txdata_dv=0 cycle is required
reg                     xmt_cnt_130b_en;                // 128b/130b Transmit Counter Enable
reg     [6:0]           xmt_cnt_130b;                   // 128b/130b Transmit Counter
wire                    xmt_cnt_130b_is_63;             // 128b/130b Transmit Counter
wire    [3:0]           xmt_sym_cnt;                    // Transmitted Symbol Counter. Allows blocks to be counted and tracked
reg     [6:0]           txstartblock_mask;              // Mask to control period of xmtbyte_txstartblock assertion
reg                     latched_fts_eieos_req;          // Request to send EIEOS after FTS Sequence completes at 8 GT/s
reg                     latched_sds_req;                // Request to send SDS
reg                     latched_g3_lpbk_eies_req;       // Request to send eieos
reg                     latched_pkt_pending;            // Latched version of xdlh_xmlh_pkt_pending
reg                     latched_in_mod_compliance;      // latch in modified compliance
wire                    eds_required;                   // Indicates EDS token is required to be inserted
wire    [3:0]           eds_sym_cnt;                    // EDS Symbol Counter. Used to insert EDS token byte-by-bye
reg                     delay_eds;                      // Delay EDS insertion if last DW of block is already in progress
wire    [(NBK*NL)-1:0]  txsyncheader;                   // Tracks current TxSyncHeader value
reg     [NL*EQTS_WD-1:0]latched_eqts_info;              // Latched EQ TS / Gen3 TS Information
wire    [NBK-1:0]       datablock_syncheader;           // Data Block Syncheader (Sized to fit DataK)
wire    [NBK-1:0]       osblock_syncheader;             // Ordered Set Block Syncheader (Sized to fit DataK)
wire    [3:0]           insert_eqts_info;               // Replace Symbol 6-9 of current TS with Equalization Information
wire    [15:4]          insert_s15_4;                   // Replace Symbol sym15 - 4 for Mod TS Format
wire                    insert_s6;                      // Replace Symbol sym6 for Modified TS Format
wire    [NL*EQTS_WD-1:0]int_eqts_info;                  // Per Lane Equalization Information to be inserted in Symbol 6-9 of current TS
wire    [NL-1:0]        int_mask_eieos;                 // Per lane masking of EIEOS Identifier



// ==================
// Internal Data Path
// ==================
reg     [NBYTE-1:0]     xdlh_xmlh_datak;                // kchar indicator from xdlh.  Combination of xdlh_xmlh_stp, sdp, eot, pad

wire    [(NB*8*NL)-1:0] cmp_data;                       // compliance data
wire    [(NBK*NL)-1:0]  cmp_datak;                      // compliance datak

reg     [DW-1:0]        pad_data;                       // xdlh data with pad symbols added
wire    [31:0]          int_pad_data;                   // pad data added to xdlh data based on current_data_rate

wire    [DW-1:0]        int_xdlh_data;                  // one chunk of combined xdlh or compliance data
wire    [NBYTE-1:0]     int_xdlh_datak;                 // k chars associated with int_xdlh_data

wire    [(NB*8*NL)-1:0] int_xmt_data;                   // one chunk of data in lane slices
// LMD: Undriven net Range
// LJ: All nets are driven
// leda NTL_CON12 off
wire    [(NBK*NL)-1:0]   int_xmt_datak;                 // k chars associated with int_xmt_data
// leda NTL_CON12 on

wire    [(NB*8*NL)-1:0] xmt_data;                       // one chunk of data in lane slices (after optional flop)
wire    [(NBK*NL)-1:0]  xmt_datak;                      // k chars associated with xmt_data

wire    [(NB*8*NL)-1:0] int_pat_data;                   // one chunk of pattern data in lane slices with link/lane # replaced
wire    [(NB*8*NL)-1:0] i_pat_data;                     // one chunk of pattern data in lane slices with link/lane # replaced
wire    [(NBK*NL)-1:0]  int_pat_datak;                  // k chars associated with pat_data

reg     [NL*8-1:0]      latched_parity;                 // even parity until previous clock
wire    [(NB*8*NL)-1:0] pat_data;                       // one chunk of pattern data in lane slices with link/lane # replaced
wire    [(NBK*NL)-1:0]  pat_datak;                      // k chars associated with pat_data

wire    [(NB*8*NL)-1:0] int_eds_data;                   // one chunk of data in lane slices used for inserting EDS token

wire    [(NB*8*NL)-1:0] int_skp_data;                   // one chunk of data in lane slices used for modifying Symbols 13-15 of SKP OS at 8 GT/s
reg     [(NB*8*NL)-1:0] int_skp_data_d;
wire    [(NB*8*NL)-1:0] int_ieds_data;                  // one chunk of data in lane slices used for cxl ieds of null flit
wire    [(NB*8*NL)-1:0] int_cmpl_data;                  // one chunk of data in lane slices used for Compliance Pattern Block 1 at 8 GT/s

wire    [NL-1:0]        xmtbyte_txelecidle_i;
wire    [NL-1:0]        xmtbyte_txelecidle_g12;
wire    [NL-1:0]        xmtbyte_txelecidle_g3;
reg     [NL-1:0]        int_txelecile_g3_txdatavalid_sync; // In gen3 or gen4 data rate, txelecidle should be deassert only right after txdatavalid==0 when upconfigure activating lanes

wire                    xmlh_pat_ack;                   // pattern command acknowledge
wire                    xmlh_pat_dv;                    // Data valid from the pattern generator
reg                     xmlh_pat_dv_d;
wire    [NBIT-1:0]      xmlh_pat_data;                  // Data from the pattern generator
wire    [NBK-1:0]       xmlh_pat_datak;                 // Data K-char indicator from the pattern generator
wire    [OS_WD-1:0]     xmlh_pat_linkloc;               // Indicates the current data contains the link # field. Its location can be inferred from active_nb
wire    [OS_WD-1:0]     xmlh_pat_laneloc;               // Indicates the current data contains the lane # field. Its location can be inferred from active_nb
wire    [3:0]           xmlh_pat_eqloc;                 // Indicates the current data contains one or more of Symbols 6-9 of a TS
wire                    xmlh_pat_eieosloc;              // Indicates the current data contains Symbol 0 of an EIEOS
wire    [15:4]          xmlh_pat_s15_4loc;              // current data with [15] = sym15, ..., [4] = sym4
wire                    xmlh_cmp_errloc;                // Indicates the current data contains the error count field. Its location can be inferred from active_nb

wire    [NBIT-1:0]      xmlh_dly_cmp_data;              // Delayed compliance Data
wire    [NBK-1:0]       xmlh_dly_cmp_datak;             // Delayed compliance K char
wire                    xmlh_dly_cmp_errloc;            // Indicates the current data contains the error count field. Its location can be inferred from active_nb

reg     [SLICE_WD-1:0]  chunk_cnt;                      // chunk counter
wire    [SLICE_WD-1:0]  chunk_offset;

reg                     curnt_compliance_finished;


reg                     xmtbyte_eidle_sent_d;
reg     [12:0]          fts_sent_cnt;
wire                    int_xmt_nfts_done;
wire                    cmd_is_fts_send;
reg                     dlyd_cmd_is_fts_send;
reg                     xmtfts_req;
wire                    fts_skip_req;                   // indicates the need to send the post FTS Skip OS
wire                    fts_eieos_req;                  // indicates the need to send an EIEOS at end of FTS sequence
reg                     latched_fts_skip_req;
reg     [3:0]           next_pat;                       // encoded command from the xmlh_byte_xmt to select a pattern to generate

wire                    short_os_pat;                   // Indicates the OS pattern indicated by next_pat is a 4 symbol pattern
wire                    short_os_4s;                    // Indicates a 1 cycle OS when active_nb[2] 4S mode
wire                    load_pat_i;
wire                    load_pat;
wire                    load_pat_easy;
wire                    cmd_is_pat;                     // the current next_pat is a os pattern (handled by this module)
wire                    next_is_pat;                    // the next cmd is a os pattern (handled by this module)
wire                    state_halt;                     // halt that is driven by state in non pkt transmission state
wire                    data_halt;
reg                     latched_eidle_sent_cmd;
reg                     goto_xmt_eidle;

wire                    os_sent;
wire                    skip_req;
wire                    skip_insert;
wire                    null_ieds_insert    = null_ieds_en & skip_insert;
wire                    null_ieds_req       = null_ieds_en & skip_req & (xbyte_next_state==S_SKIP); // skip_req & it is accepted by the state machine
wire                    or_sdp;
wire                    or_stp;
wire                    or_eot;
wire                    valid_sdp;
reg     [4:0]           accumed_skips; // need more bits because in sris mode a 4096 byte payload packet can accumulate up to 4096/153=27 skips
wire    [4:0]           prev_accumed_skips; // need more bits because in sris mode a 4096 byte payload packet can accumulate up to 4096/153=27 skips
wire                    accumed_skips_is_0;
wire                    accumed_skips_is_not_0;
reg                     latched_int_xmt_nfts_done;
wire    [NW-1 :0]       int_xdlh_pad;

wire    [NW-1 :0]       xdlh_eot_or_pad;                // bitwise or of xdlh_xmlh_eot and xdlh_xmlh_pad
wire    [NW-1 :0]       xdlh_sp_or_pad;                 // bitwise or of xdlh_xmlh_sdp, xdlh_xmlh_stp, and xdlh_xmlh_pad
reg     [7:0]           latched_ts_cntrl;               // training sequence control
reg                     latched_mod_ts;
reg     [56-1:0]        latched_alt_prot_info;          // Alternate protocol info for sym14-8
reg                     latched_ts_alt_protocol;        // Alternate protocol
reg                     ltssm_ts_auto_change_d;
reg                     ltssm_ts_auto_change_r;
reg                     latched_ts_auto_change_i;
reg     [4:0]           latched_ts_data_rate_i;
reg     [7:0]           int_xlinknum;                   // 8bits indicate link number to be inserted in training sequence
reg     [NL-1:0]        int_xk237_4lnknum;              // 1 bit per lane, 1 -- K237, 0 -- send lane number
reg     [NL-1:0]        int_xk237_4lannum;              // 1 bit per lane, 1 -- K237, 0 -- send link number
wire    [NL-1:0]        insert_linknum;
wire    [NL-1:0]        insert_lanenum;
wire                    cfg_compliance_sos;         // Send skips during compliance

reg                     latched_eies_sent_sym;          // 8 EIE Symbols Sent
reg                     latched_eieos_sent;             // EIEOS Sent
wire                    eieos_required;                 // EIEOS Required
reg     [15:0]          eieos_req_cnt;                  // EIEOS Required OS Counter
wire                    eieos_req_cnt_is_31;
wire                    eieos_req_cnt_is_ts1_num;
wire                    eieos_cnt_en;                   // EIEOS Required Counter Enable
wire                    n_fts_eieos_cnt_en;             // EIEOS Required Counter Enable used when sending FTS at Gen3 Rate
wire                    lfsr_rollover_eieos_req;        // EIEOS Required due to LFSR Rollover during Recovery.Equalization
wire                    int_any_mask_eieos;             // mask EIEOS on any lane
reg                     int_any_mask_eieos_r;           // mask EIEOS on any lane
wire                    any_mask_eieos_fall_edge;       // falling edge detect for mask EIEOS signal on any lane
reg                     rollover_eieos_req;             // EIEOS due to LFSR Rollover Requested. Prevents EIEOS Masking
wire    [OS_WD-1:0]     xmlh_cmpl_os_sync_header;       // Gen3 Mod_compliance/compliance OS sync header
wire    [OS_WD*2-1:0]   xmlh_cmpl_block_count;          // indicate that Compliance pattern Block count is equal to 1 and 2 in 128b/130b and 4 and 5 in 1b/1b
wire    [OS_WD-1:0]     xmlh_mod_cmpl_skp;              // indicate it is time to build SKP OS in Mod Compliance Pattern
wire    [OS_WD-1:0]     xmlh_pat_in_ctlskip;            // SKP OS indication of the Enhanced SKIP Pattern
reg                     xmlh_pat_in_ctlskip_d;          // SKP OS indication of the Enhanced SKIP Pattern
reg                     xmtbyte_ctlskip_sent_d;
wire    [NL*24-1:0]     skp_margin_sym;
reg     [1:0]           latched_ctlskp_req;             // Request to send the enhanced skip-os
reg                     in_data_stream;                 // current pat is in data stream
reg                     next_ctlskip_pat_req;           // next pat is the enhanced SKP-OS
wire                    next_ctlskip_pat;               // indicates the enhanced skip-os send with (next_cmd==`EPX16_SEND_SKP)
reg                     int_lpbkentry_d;
wire                    int_lpbkentry_pulse;            // create a pulse at the begining of Loopback.Entry
wire                    ltssm_lpbk_master;
reg                     skp_after_eds;                  // Insert SKP after EDS (if EDS was triggered by SKP request)
reg                     eds_inserted_4_skp;             // Indicates EDS has been asserted for SKP request
reg     [3:0]           next_cmd_d;                     // 1 cycle delayed next_cmd
reg     [3:0]           next_pat_d;                     // 1 cycle delayed next_pat

wire                    xmtbyte_sds_sent;
wire                    xmtbyte_eidle_fts_idle_sds_sent;
wire                    xmtbyte_loe_2_ts1_ec_00b_sent;  // less or equal to 2 ts1s with ec==00b sent
reg     [10:0]          xmtbyte_ts_pcnt;
wire                    xmtbyte_ts_data_diff;           // current ts data is different from previous
reg     [10:0]          xmtbyte_ts_cnt;
reg     [3:0]           ts_sym_cnt;
wire                    xmtbyte_1024_ts_sent;
wire                    xmtbyte_spd_chg_sent;
wire                    xmtbyte_dis_link_sent;
reg                     ts_speed_change_sent;
reg                     ts_disable_link_sent;
reg     [NL*8*16*NB-1:0]latched_xmtbyte_txdata;         // a TS OS
reg     [OS_WD-1:0]     xmtbyte_ints1;
reg     [OS_WD-1:0]     xmtbyte_ints2;
reg     [OS_WD-1:0]     xmtbyte_ints;
reg                     xmtbyte_ineieos;
reg                     xmtbyte_inskip;

wire                    ltssm_ts_cnt_en;

reg                     data_stream_window;   // only data stream in the window
wire                    xmtbyte_skip_insert = skip_insert;



wire   ltssm_eq = smlh_ltssm_state == `EPX16_S_RCVRY_EQ0 | smlh_ltssm_state == `EPX16_S_RCVRY_EQ1 | smlh_ltssm_state == `EPX16_S_RCVRY_EQ2 | smlh_ltssm_state == `EPX16_S_RCVRY_EQ3; 
reg    ltssm_eq_d;

always @( posedge core_clk or negedge core_rst_n ) begin : ltssm_eq_d_PROC
    if ( ~core_rst_n ) begin
        ltssm_eq_d <= #TP 0;
        ts1_sent_d <= #TP 0;
    end else begin
        ltssm_eq_d <= #TP ltssm_eq;
        ts1_sent_d <= #TP xmtbyte_ts1_sent[0];
    end
end // ltssm_eq_d_PROC

wire   ltssm_eq_rlock_i = ltssm_eq_d & smlh_ltssm_state == `EPX16_S_RCVRY_LOCK; // first clock cycle in R.Lock from R.Eq
// do not send the eieos again at the eq->R.Lock if the eieos is on the transmitting (latched_eieos_sent == 1) at the eq->R.Lock
// if at the ltssm_eq_rlock_i pulse and xmtbyte_ts1_sent at its previous clock, the controller will send the scheduled TS1 and then an eieos when entry to R.Lock from EQ
wire   ltssm_eq_rlock   = (ltssm_eq_rlock_i & (ts1_sent_d | ~latched_eieos_sent));
wire   ltssm_eq_rlock_eieos_sent = (current_data_rate != `EPX16_GEN1_RATE) & ((smlh_ltssm_state == `EPX16_S_LPBK_ACTIVE) | (ltssm_eq_rlock_i & latched_eieos_sent & ~ts1_sent_d));

// have to delay the enable because of ltssm_cmd delayed too with ltssm_cmd_is_norm
//assign ltssm_ts_cnt_en = ltssm_ts_cnt_en_i & ~|ltssm_cmd_is_norm[CMD_NORM_WD-1:0];
assign ltssm_ts_cnt_en = ltssm_eq_rlock_eieos_sent | (ltssm_ts_cnt_en_i & ~|ltssm_cmd_is_norm[CMD_NORM_WD-1:0]);          // do not send the eieos again at the eq->R.Lock if the eieos is on the transmitting (latched_eieos_sent == 1) at the eq->R.Lock

reg     [3:0]    xbyte_curnt_state_d;
wire             xbyte_curnt_state_exit_skip = (xbyte_curnt_state_d == S_SKIP && xbyte_curnt_state != S_SKIP);


always @( posedge core_clk or negedge core_rst_n ) begin : flit_null_d_r_PROC
    if ( ~core_rst_n )
        flit_null_d_r <= #TP 0;
    else if ( flit_null_d )
        flit_null_d_r <= #TP 1'b1;
    else if ( pid_rcvd_d || os_sent_d || |xmtbyte_ts1_sent || |xmtbyte_ts2_sent )
        flit_null_d_r <= #TP 0;
end // flit_null_d_r_PROC

assign latched_flit_null_d = 1'b0;


//delay ltssm_cmd `EPX16_NORM in Recovery.RcvrLock so that the packet to be transmitted completes at current data rate
//to prevent gen1/2 format packets to be transmitted at gen3 rate, or vice versa
always @( posedge core_clk or negedge core_rst_n ) begin : ltssm_cmd_is_norm_PROC
    if ( ~core_rst_n ) begin
        ltssm_cmd_is_norm[CMD_NORM_WD:0] <= #TP 0;
    end else begin
        ltssm_cmd_is_norm[CMD_NORM_WD:0] <= #TP {ltssm_cmd_is_norm[CMD_NORM_WD-1:0], (ltssm_cmd_i == `EPX16_NORM)};
    end
end // ltssm_cmd_is_norm_PROC


    // ~smlh_in_l0 to block Tx pkt delays one more cycle but ltssm_cmd not delayed for the parameter
assign ltssm_cmd = 
                   (|latched_ctlskp_req) ?  `EPX16_SEND_IDLE : 
                   (|ltssm_cmd_is_norm[CMD_NORM_WD-1:0] && ltssm_cmd_i == `EPX16_SEND_TS1) ? `EPX16_NORM :
                    ltssm_cmd_i;

//
//count TS sent
//
//SDS sent
always @( posedge core_clk or negedge core_rst_n ) begin : smlh_gen3_error_count_d_PROC
    if ( ~core_rst_n )
        smlh_gen3_error_count_d <= #TP 0;
    else
        smlh_gen3_error_count_d <= #TP smlh_gen3_error_count_i;
end // smlh_gen3_error_count_d_PROC

assign smlh_gen3_error_count = (int_active_nb==1 && xmt_sym_cnt == 15) ? smlh_gen3_error_count_d : smlh_gen3_error_count_i;

assign xmtbyte_sds_sent                = (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? (xmtbyte_txdata[7:0] == 8'hE1) & (xmtbyte_txdatak[1:0] == `EPX16_SYNC_OS_BLOCK) & xmtbyte_txstartblock[0] : 0;
assign xmtbyte_eidle_fts_idle_sds_sent = xmtbyte_fts_sent | xmtbyte_idle_sent | xmtbyte_eidle_sent | xmtbyte_sds_sent;

//symbol count
always @( posedge core_clk or negedge core_rst_n ) begin : ts_sym_cnt_PROC
    if ( ~core_rst_n ) begin
        ts_sym_cnt <= #TP 0;
    end else if ( |xmtbyte_ts1_sent || |xmtbyte_ts2_sent ) begin
        ts_sym_cnt <= #TP 0;
    end else if ( ((ts_sym_cnt + 1) << active_nb_shift_os) >= 16 ) begin
        ts_sym_cnt <= #TP ts_sym_cnt;
    end else if ( xmtbyte_txdata_dv ) begin
        ts_sym_cnt <= #TP ts_sym_cnt + 1;
    end
end // ts_sym_cnt_PROC

//xmt is in ts1 transmission
always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_ints1_PROC
    if ( ~core_rst_n ) begin
        xmtbyte_ints1 <= #TP 0;
    end else if ( |xmtbyte_ts1_sent ) begin
        xmtbyte_ints1 <= #TP xmtbyte_ts1_sent;
    end else if ( xmtbyte_eidle_fts_idle_sds_sent || |xmtbyte_ts2_sent || xmtbyte_skip_sent || xmtbyte_eies_sent ) begin
        xmtbyte_ints1 <= #TP 0;
    end
end // xmtbyte_ints1_PROC

//xmt is in ts2 transmission
always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_ints2_PROC
    if ( ~core_rst_n ) begin
        xmtbyte_ints2 <= #TP 0;
    end else if ( |xmtbyte_ts2_sent ) begin
        xmtbyte_ints2 <= #TP xmtbyte_ts2_sent;
    end else if ( xmtbyte_eidle_fts_idle_sds_sent || |xmtbyte_ts1_sent || xmtbyte_skip_sent || xmtbyte_eies_sent ) begin
        xmtbyte_ints2 <= #TP 0;
    end
end // xmtbyte_ints2_PROC

//xmt is in ts transmission
always @( * ) begin : xmtbyte_ints_PROC
    xmtbyte_ints = 0;

    xmtbyte_ints = xmtbyte_ints1 | xmtbyte_ints2;
end //xmtbyte_ints_PROC

//xmt is in skip transmission
always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_inskip_PROC
    if ( ~core_rst_n ) begin
        xmtbyte_inskip <= #TP 0;
    end else if ( xmtbyte_skip_sent ) begin
        xmtbyte_inskip <= #TP 1;
    end else if ( |xmtbyte_ts1_sent || |xmtbyte_ts2_sent || xmtbyte_eidle_fts_idle_sds_sent || xmtbyte_eies_sent ) begin
        xmtbyte_inskip <= #TP 0;
    end
end // xmtbyte_inskip_PROC

//xmt is in eieos transmission
always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_ineieos_PROC
    if ( ~core_rst_n ) begin
        xmtbyte_ineieos <= #TP 0;
    end else if ( xmtbyte_eies_sent ) begin
        xmtbyte_ineieos <= #TP 1;
    end else if ( |xmtbyte_ts1_sent || |xmtbyte_ts2_sent || xmtbyte_eidle_fts_idle_sds_sent || xmtbyte_skip_sent ) begin
        xmtbyte_ineieos <= #TP 0;
    end
end // xmtbyte_ineieos_PROC

//latch transmit TS data in order to compare with the next transmit TS data
always @( posedge core_clk or negedge core_rst_n ) begin : latched_xmtbyte_txdata_PROC
    if ( ~core_rst_n ) begin
        latched_xmtbyte_txdata <= #TP 0;
    end else if ( ~(|xmtbyte_ints || xmtbyte_inskip || xmtbyte_ineieos) ) begin
        latched_xmtbyte_txdata <= #TP 0;
    end else if ( |xmtbyte_ints && xmtbyte_txdata_dv ) begin
        // LMD: Range index out of bound
        // LJ: latched_xmtbyte_txdata is not out of index bound because ts_sym_cnt is reset to 0 whenever xmtbyte_ts_sent in xmtbyte_ints window
        // leda E267 off
        latched_xmtbyte_txdata[ts_sym_cnt[S_WD-1:0]*NB*8*NL +: NB*8*NL] <= #TP xmtbyte_txdata[0 +: NB*8*NL];
        // leda E267 on
    end
end // latched_xmtbyte_txdata_PROC

//persistency count
//note that xmtbyte_ts1_sent/xmtbyte_ts2_sent is a pulse and always one cycle earlier than the real ts data sent out

// LMD: Range index out of bound
// LJ: latched_xmtbyte_txdata is not out of index bound because ts_sym_cnt is reset to 0 whenever xmtbyte_ts_sent in xmtbyte_ints window
// leda E267 off
assign xmtbyte_ts_data_diff = xmtbyte_txdata_dv && |xmtbyte_ints && (xmtbyte_txdata[0 +: NB*8*NL] != latched_xmtbyte_txdata[ts_sym_cnt[S_WD-1:0]*NB*8*NL +: NB*8*NL]);
// leda E267 on

always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_pcnt_PROC
    if ( ~core_rst_n ) begin
        xmtbyte_ts_pcnt <= #TP 0;
    end else if ( ltssm_clear ) begin //clear pcnt if state transition
        if ( xmtbyte_ts1_sent[0] || xmtbyte_ts2_sent[0]) begin
            xmtbyte_ts_pcnt <= #TP 1;
        end else begin
            xmtbyte_ts_pcnt <= #TP 0;
        end
    end else if ( (~xmtbyte_txdata_dv && ~(|xmtbyte_ts1_sent || |xmtbyte_ts2_sent || xmtbyte_skip_sent || xmtbyte_eies_sent)) ||
                  xmtbyte_eidle_fts_idle_sds_sent ) begin
        //~txdata_dv with nothing going to sent, fts sent, idle sent, eios sent, sds sent. ~xmtbyte_txdata_dv is always at the end of a block for gen3/4
        xmtbyte_ts_pcnt <= #TP 0;
    end else if ( xmtbyte_ts_data_diff ) begin
        //reset if current transmitting data is different from previous in ts window
        if ( xmtbyte_ts1_sent[0] || xmtbyte_ts2_sent[0]) begin //set to 2 if it is the last cycle of the TS
            xmtbyte_ts_pcnt <= #TP 2;
        end else begin
            xmtbyte_ts_pcnt <= #TP 1; //set to 1 if it is in the mid of the TS
        end
    end else if ( &xmtbyte_ts_pcnt != 1'b1 ) begin
        if ( xmtbyte_ts1_sent[0] || xmtbyte_ts2_sent[0]) begin
            xmtbyte_ts_pcnt <= #TP xmtbyte_ts_pcnt + 1;
        end
    end
end // xmtbyte_pcnt_PROC


//xmtbyte_ts_cnt is purely a ts1/2 sent count. No TS content comparison to reset the count
always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_cnt_PROC
    if ( ~core_rst_n ) begin
        xmtbyte_ts_cnt <= #TP 0;
    end else if ( ltssm_clear ) begin //clear count if state transition
        if ( xmtbyte_ts1_sent[0] || xmtbyte_ts2_sent[0]) begin
            xmtbyte_ts_cnt <= #TP 1;
        end else begin
            xmtbyte_ts_cnt <= #TP 0;
        end
    end else if ( (xmtbyte_ints1[0] && xmtbyte_ts2_sent[0]) || (xmtbyte_ints2[0] && xmtbyte_ts1_sent[0]) ) begin
        xmtbyte_ts_cnt <= #TP 1; //set to 1 if there is a ts1 -> ts2 or ts2 -> ts1 transition
    end else if ( &xmtbyte_ts_cnt != 1'b1 ) begin
        if ( xmtbyte_ts1_sent[0] || xmtbyte_ts2_sent[0]) begin
            xmtbyte_ts_cnt <= #TP xmtbyte_ts_cnt + 1;
        end
    end
end // xmtbyte_cnt_PROC


localparam CNT_16 = 16 
;
localparam CNT_1024 = 1024 
;

 assign xmtbyte_1024_ts_sent           = (cfg_fast_link_mode & (xmtbyte_ts_cnt>=CNT_16)) | (~cfg_fast_link_mode & (xmtbyte_ts_cnt>=CNT_1024)); //for Polling.Active

assign xmtbyte_loe_2_ts1_ec_00b_sent  = (xmtbyte_ts_pcnt<=2) & (latched_eqts_info[1:0] == 2'b00); //no more than 2 ts1 with ec==00b sent. This signal is only used in R.Lock (send TS1 only), so no need xmtbyte_ts1_sent
always @( posedge core_clk or negedge core_rst_n ) begin : ts_speed_change_sent_PROC
    if ( ~core_rst_n ) begin
        ts_speed_change_sent <= #TP 0;
    end else if ( xmtbyte_ints[0] && ((active_nb == 1 && ts_sym_cnt == 4) || (active_nb == 2 && ts_sym_cnt == 2) || (active_nb == 4 && ts_sym_cnt == 1)) ) begin
        ts_speed_change_sent <= #TP xmtbyte_txdata[7];
    end
end // ts_speed_change_sent_PROC

assign xmtbyte_spd_chg_sent = ts_speed_change_sent;

always @( posedge core_clk or negedge core_rst_n ) begin : ts_disable_link_sent_PROC
    if ( ~core_rst_n ) begin
        ts_disable_link_sent <= #TP 0;
    end else if ( xmtbyte_ints[0] && active_nb == 1 && ts_sym_cnt == 5 ) begin
        ts_disable_link_sent <= #TP xmtbyte_txdata[1];
    end else if ( xmtbyte_ints[0] && ((active_nb == 2 && ts_sym_cnt == 2) || (active_nb == 4 && ts_sym_cnt == 1)) ) begin
        ts_disable_link_sent <= #TP xmtbyte_txdata[9];
    end
end // ts_disable_link_sent_PROC

assign xmtbyte_dis_link_sent = ts_disable_link_sent;

//pcie_perf

wire eot_is_last;
wire sot_is_last;
wire sdp_is_last;

wire nw_gtr_2;
wire next_eot_is_last;
// =============================================================================

assign nw_gtr_2 = (NW > 2); // CX_PL_NW for layer1 is equal to CX_NW for layer2,3

assign  int_active_nb = {2'b00,active_nb};

// This path is very strict timing. Improve timing on gen3/4 logic
reg xmlh_skip_pending_gen3;
always @(posedge core_clk or negedge core_rst_n) begin
    if (!core_rst_n) begin
        xmlh_skip_pending_gen3 <= #TP 0;
    end else begin
                                 // make a timing from the previous conditions
                                 //   skip_req & (!eot_is_last | eds_required)
        xmlh_skip_pending_gen3 <= #TP ((skip_req & |prev_accumed_skips) || |prev_accumed_skips)
                                      && (!next_eot_is_last || (data_stream_in_progress && !ltssm_in_compliance));
    end
end
assign xmlh_skip_pending =  (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? xmlh_skip_pending_gen3 : skip_req & !eot_is_last;

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n) begin
        xmtbyte_txcompliance_cmp_i <= #TP 0;
    end else if ( ~ltssm_in_compliance ) begin // clear txcompliance_cmp for 1337818
        xmtbyte_txcompliance_cmp_i <= #TP 0; // clear txcompliance_cmp to not make txelecidle=1 and txcompliance=1 for 2 clocks
    end else begin
        xmtbyte_txcompliance_cmp_i <= #TP 
                        (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? 0 :
                                           {NL{(xbyte_curnt_state == S_CMP) && os_start}};
    end

assign xmtbyte_txcompliance_turnoff_i = ~(int_lanes_active | smlh_no_turnoff_lanes);

assign xmtbyte_txcompliance_i = (xmtbyte_txcompliance_cmp_i | xmtbyte_txcompliance_turnoff_i);

assign  cmd_is_data = ((xbyte_curnt_state == S_XPKT_WAIT4_START) || (xbyte_curnt_state == S_XPKT_WAIT4_STOP));
assign  xmtbyte_cmd_is_data =
 cmd_is_data;

//during (ltssm_cmd == `EPX16_SEND_EIDLE), Tx may transmit SKP + pending pkts first. In this case it is !cmd_is_pat
//In Recovery.RcvrLock, Tx may be still sending pkts. In this case it is !cmd_is_pat
assign  cmd_is_pat  = ((ltssm_cmd == `EPX16_SEND_EIDLE) && !( xdlh_xmlh_pkt_pending && latched_pkt_pending))   || (ltssm_cmd ==  `EPX16_SEND_N_FTS)
                      || ((ltssm_cmd == `EPX16_SEND_TS1) && !( xdlh_xmlh_pkt_pending | data_stream_in_progress)) || (ltssm_cmd ==  `EPX16_SEND_TS2)
                      || (ltssm_cmd == `EPX16_MOD_COMPL_PATTERN)
                      || (ltssm_cmd == `EPX16_COMPLIANCE_PATTERN);

assign  next_is_pat  = (xbyte_next_state == S_EIDLE)   || (xbyte_next_state == S_FTS)
                      || (xbyte_next_state == S_TS)
                      || (xbyte_next_state == S_EIES) || (xbyte_next_state == S_EIES_SYM)
                      || (xbyte_next_state == S_SDS)
                      || (xbyte_next_state == S_SKIP) || (xbyte_next_state == S_CMP)
                      ;

assign ltssm_lpbk_master = lpbk_master;

// When exiting a Data Stream, only SKP, EIOS or EIEOS Ordered Sets may appear
// in the Block following the EDS Token.
assign  eds_required = (((next_cmd == S_SKIP) & ~cxl_mode_enable) || (next_cmd == S_EIDLE) || (next_cmd == S_EIES)) /* & ~ltssm_in_lpbkentry */ //no eds in loopback.entry handdled in xmt_data
                        && data_stream_in_progress && !ltssm_in_compliance && ~(smlh_ltssm_state == `EPX16_S_LPBK_EXIT_TIMEOUT) && (current_data_rate >= `EPX16_GEN3_RATE); // no eds in S_LPBK_EXIT_TIMEOUT or gen1/2 rate

// Delay EDS insertion until next Data Block if last DW of current Data Block
// is already in progess. This is only required in 1sx1, 2sx2 and 1sx2 mode of
// operation.
always @(posedge core_clk or negedge core_rst_n)
begin : delay_eds_proc
    if (!core_rst_n)
        delay_eds <= #TP 1'b0;
    else if (xmt_sym_cnt == 4'b0000)
        delay_eds <= #TP 1'b0;
    else if ((xbyte_curnt_state != S_EDS) && (xbyte_next_state == S_EDS))
        delay_eds <= #TP (((int_link_mode[0] && |int_active_nb[1:0]) ||    // 1sx1 and 2sx1
                          (int_link_mode[1] && int_active_nb[0]))    &&    // 1sx2
                          |xmt_sym_cnt[3:2])                               // Current Symbol Count >= 12 (i.e. last DW)
                          ;
end

assign eds_sym_cnt = (delay_eds) ? 4'b0000 : xmt_sym_cnt;

// for eieos/eios, detect null flit and convert it to iEDS and at the next null flit to send eieos/eios
wire eds_byte_t;
assign eds_byte_t   = ( (active_nb == 4) ? (xmt_sym_cnt == 12) : (xmt_sym_cnt == 14));
wire cxl_full_wd  = (active_nb == 4 && int_link_mode == MAX_NL_NB4);
wire eds_time_i   = (cxl_full_wd ? flit_null_d : latched_flit_null_d) & eds_byte_t;
wire int_eds_time = (cxl_full_wd ? flit_null_d : latched_flit_null_d) & ( (active_nb == 4) ? ( int_link_mode == 8 ? (xmt_sym_cnt == 4 & xdlh_xmlh_pid != 16'h8000) : xmt_sym_cnt == 12) : (xmt_sym_cnt == 14));
wire ds_eds_on    = eds_on & int_xmt_data != 0; //downsized linkwidth eds on going
reg  eds_time_d, ieds_time, int_eds_time_d, ds_eds_on_d;
always @(posedge core_clk or negedge core_rst_n) begin : eds_time_d_PROC
    if ( ~core_rst_n ) begin
        eds_time_d <= #TP 1'b0;
        int_eds_time_d <= #TP 1'b0;
        ds_eds_on_d <= #TP 0;
    end else if ( xbyte_next_state != S_EDS || eds_inserted ) begin
        eds_time_d <= #TP 1'b0;
        int_eds_time_d <= #TP 1'b0;
        ds_eds_on_d <= #TP 0;
    end else begin
        if ( eds_time_i )
            eds_time_d <= #TP 1'b1;

        if ( int_eds_time )
            int_eds_time_d <= #TP 1'b1;

        if ( ds_eds_on )
            ds_eds_on_d <= #TP 1;
    end
end // eds_time_d_PROC

wire ds_eds_on_i = ds_eds_on | ds_eds_on_d;  // downsized eds on going
wire ds_eds_time = ds_eds_on_i & eds_byte_t; // time to insert eds for downsized link width

always @(posedge core_clk or negedge core_rst_n) begin : ieds_time_PROC
    if ( ~core_rst_n )
        ieds_time <= #TP 1'b0;
    else if ( xbyte_next_state != S_EDS || eds_inserted )
        ieds_time <= #TP 1'b0;
    else
        ieds_time <= #TP (xbyte_next_state == S_EDS && xbyte_curnt_state != S_EDS);
end // ieds_time_PROC

// for narrow linkwidth to send eios/eieos, hold the null flit over one clocks and then send eios/eieos at the next null flit time zone
wire eds_time, eds_time_insert;
assign eds_time        = ( (active_nb == 4 && int_link_mode < MAX_NL_NB4)) ? int_eds_time_d : eds_time_i;
assign eds_time_insert = ( (active_nb == 4 && int_link_mode < MAX_NL_NB4)) ? ds_eds_time    : eds_time_i;
//wire eds_time_insert = (`ifdef EPX16_CX_NB_GTR_4 (active_nb >= 8 && int_link_mode < 8) || `endif (active_nb == 4 && int_link_mode < 16)) ? (eds_time_i & eds_time_d) : eds_time_i;
//wire eds_time = `ifdef EPX16_CX_NB_GTR_4 (active_nb >= 8) ? (xmt_sym_cnt >= 8) : `endif (active_nb == 4) ? (xmt_sym_cnt == 12) : (xmt_sym_cnt == 14);

// Indicates that last byte of EDS token has been inserted into a Data Block
// for cxl, insert iEDS instead of EDS. if narrow linkwidth ( < 512 bits ), must insert eios/eieos in the next null flit
//wire   ieds_time_insert = ltssm_cxl_sh_bypass ? ieds_time : (flit_null_i & eds_time_insert);
wire   ieds_time_insert = ltssm_cxl_sh_bypass ? ieds_time : eds_time_insert;
assign eds_on           = (xbyte_curnt_state == S_EDS) ? (cxl_mode_enable ? eds_time         : EndOfEDS( int_active_nb, eds_sym_cnt )) : 1'b0;
assign eds_inserted     = (xbyte_curnt_state == S_EDS) ? (cxl_mode_enable ? ieds_time_insert : EndOfEDS( int_active_nb, eds_sym_cnt )) : 1'b0;

// Data Stream Tracking
// A Data Stream starts with the first Symbol of the Data Block that
// follows an SDS Ordered Set. It ends either when a Framing Error
// is detected or with the last Symbol of the Data Block that precedes
// an Ordered Set other than a SKP Ordered Set.
always @(posedge core_clk or negedge core_rst_n)
begin : data_stream_PROC
    if (!core_rst_n)
        data_stream_in_progress <= #TP 1'b0;
    else if (xbyte_next_state == S_SDS)
        data_stream_in_progress <= #TP 1'b1;
    else if (xmtbyte_eidle_sent || xmtbyte_eies_sent)
        data_stream_in_progress <= #TP 1'b0;
end

// Store condition when a Data Stream is interrupted due to SKP
// insertion. Ensures SKP is inserted in case where LTSSM cmd changes
// while EDS is being inserted prior to the SKP OS
always @(posedge core_clk or negedge core_rst_n)
begin : skp_after_eds_PROC
    if (!core_rst_n)
        skp_after_eds <= #TP 1'b0;
    else if (((xbyte_curnt_state == S_XPKT_WAIT4_START) ||
              (xbyte_curnt_state == S_XPKT_WAIT4_STOP)  ||
              (xbyte_curnt_state == S_SDS)              ||
              (xbyte_curnt_state == S_IDLE))            &&
              eds_required && (next_cmd == S_SKIP))
        skp_after_eds <= #TP 1'b1;
    else if (xmtbyte_skip_sent)
        skp_after_eds <= #TP 1'b0;
end

// Set when EDS has been inserted prior to a SKP
// and only cleared when SKP is sent
always @(posedge core_clk or negedge core_rst_n)
begin : eds_inserted_4_skp_PROC
    if (!core_rst_n)
        eds_inserted_4_skp <= #TP 1'b0;
    else if ((next_cmd == S_SKIP) && eds_inserted)
        eds_inserted_4_skp <= #TP 1'b1;
    else if (xmtbyte_skip_sent)
        eds_inserted_4_skp <= #TP 1'b0;
end

reg curnt_is_short;
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
         curnt_is_short    <= #TP 1'b0;
    else if (load_pat)
         curnt_is_short    <= #TP short_os_4s;

// Delay assertion of load_pat if tranmit counter is about to roll over
// and next command is an OS pattern
assign delay_load_pat = (xmt_cnt_130b_is_63 & ~ltssm_cxl_sh_bypass); // 7'h3f

// Latch load_pat if xmtbyte_txdata_dv=0 cycle is pending.
// Used to delay assertion of load_pat by one cycle
always @(posedge core_clk or negedge core_rst_n)
begin : latched_load_pat_proc
    if (!core_rst_n) begin
        latched_load_pat <= #TP 0;
    end else begin
      if (delay_load_pat && next_is_pat && os_end)
        latched_load_pat <= #TP 1;
      else
        latched_load_pat <= #TP 0;
    end
end

// delay the eq ts symbol information when xmtbyte_txdata_dv=0
//  SEND_IDLE: SDS-OS -> Idle token , SEND_EIDLE: EDS token -> EI-OS, SEND_TS1: EDS token -> EIE-OS
wire ltssm_ts_halt;
wire ltssm_ts_halt_next;
always @(posedge core_clk or negedge core_rst_n)
begin : delay_xbyte_curnt_state_d
    if (!core_rst_n) begin
        xbyte_curnt_state_d <= #TP S_IDLE;
    end else begin
        xbyte_curnt_state_d <= #TP xbyte_curnt_state;
    end
end

assign ltssm_ts_halt = (state_halt && ((ltssm_cmd == `EPX16_SEND_IDLE)||(ltssm_cmd == `EPX16_SEND_EIDLE)));
assign ltssm_ts_halt_next = (state_halt && ((ltssm_cmd == `EPX16_SEND_IDLE)||(ltssm_cmd == `EPX16_SEND_EIDLE)||((ltssm_cmd == `EPX16_SEND_TS1) && (xbyte_curnt_state_d == S_EDS))));

// Latched EQ TS Equalization Information
always @(posedge core_clk or negedge core_rst_n)
begin : latched_eqts_proc
    if (!core_rst_n) begin
        latched_eqts_info <= #TP 0;
    end
    // if enter S_RCVRY_SPEED from a very short S_RCVRY_RCVRCFG because of EIOS received & ~Rx_TS2, the core may Tx TS2 in S_RCVRY_SPEED without updated eqctl_eqts_info because of ltssm_ts_halt (~TxDataValid).
    // eqctl_eqts_info[31:16] == {8'h45,8'h45} means sending TS2 but not TS1 in S_RCVRY_SPEED, latched_eqts_info[31:16] != eqctl_eqts_info[31:16] means latched_eqts_info did not get update to eqctl_eqts_info.
    // replacing load_pat_easy with load_pat_i won't affect the logic functionality. Doing so is to improve synthesis timing
    else if ( load_pat_i && smlh_ltssm_last == `EPX16_S_RCVRY_RCVRCFG && smlh_ltssm_state == `EPX16_S_RCVRY_SPEED && eqctl_eqts_info[31:16] != latched_eqts_info[31:16] && eqctl_eqts_info[31:16] == {2{`EPX16_TS2_8B}} ) begin
        latched_eqts_info <= #TP eqctl_eqts_info;
    end else if (ltssm_ts_halt) begin
        latched_eqts_info <= #TP latched_eqts_info;
    end
    else if (load_pat_i) begin
        latched_eqts_info <= #TP eqctl_eqts_info;
    end
end

// If EIEOS Masking if active on any Lane during Equalization and
// the Scrambler LFSR rolls over, disable EIEOS Masking and insert EIEOS
// on all lanes the next time 32 consecutive TS1s are transmitted
assign lfsr_rollover_eieos_req = (|eqctl_mask_eieos && |scramble_lfsr_rollover);

always @(posedge core_clk or negedge core_rst_n)
begin : rollover_eieos_req_PROC
    if (!core_rst_n)
        rollover_eieos_req <= #TP 1'b0;
    else if (xmlh_pat_eieosloc)        // Clear when next unmasked EIEOS is inserted
        rollover_eieos_req <= #TP 1'b0;
    else if (lfsr_rollover_eieos_req)  // Set when roll over condition occurs
        rollover_eieos_req <= #TP 1'b1;
end

// mask EIEOS on any lane
assign int_any_mask_eieos = |eqctl_mask_eieos & EIEOS_MASK_LOGIC_ENABLE==0;

//gates: when int_any_mask_eieos signal goes away, eieos_cnt reset to 0
always @(posedge core_clk or negedge core_rst_n) begin : any_mask_eieos_fall_edge_PROC
    if (!core_rst_n)
        int_any_mask_eieos_r <= #TP 1'b0;
    else
        int_any_mask_eieos_r <= #TP int_any_mask_eieos;
end

assign any_mask_eieos_fall_edge = !int_any_mask_eieos & int_any_mask_eieos_r & EIEOS_MASK_LOGIC_ENABLE==0;

assign  load_pat_i  =
                      (!delay_load_pat || latched_load_pat) &&
                      ( os_end || (short_os_4s && curnt_is_short));


assign  load_pat    = next_is_pat && ( load_pat_i);
// Improved timing closure: use the ltssm_cmd insted of the next_is_pat(xbyte_next_state)
assign  load_pat_easy =                        load_pat
                        ;


assign  short_os_pat= (next_pat == `EPX16_SEND_SKP) || (next_pat == `EPX16_SEND_N_FTS);

assign  short_os_4s = short_os_pat &&
                      (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE) &&
                      int_active_nb[2];

// combine xdlh_xmlh special symbol signals into a per byte kchar signal
assign  xdlh_eot_or_pad         = xdlh_xmlh_eot | int_xdlh_pad;
assign  xdlh_sp_or_pad          = xdlh_xmlh_sdp | xdlh_xmlh_stp | int_xdlh_pad;


always @(*) begin : XDLH_PAD
    integer i;
    for(i = 0; i < NW; i = i + 1) begin
        xdlh_xmlh_datak[i*4 +: 4] =
            {xdlh_eot_or_pad[i], int_xdlh_pad[i],
            int_xdlh_pad[i], xdlh_sp_or_pad[i]};
    end
end

// If DLLP is a PM DLLP {DLLP Type = 8'b0010xxxx}, check that SDP Token is
// correct with regard to current_data_rate. Mask DLLP and insert IDL if SDP
// token is incorrect for current_data_rate
always @(*) begin : XDLH_XMLH_DATA
   integer i;
   for(i = 0; i < NW; i = i + 1) begin
       mask_sdp[i] = (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE) ?
            xdlh_xmlh_data[i*32 +: 16] == `EPX16_SDP_16B &&
            xdlh_xmlh_data[i*32+20 +: 4] == 4'b0010  :
            xdlh_xmlh_data[i*32 +: 8] == `EPX16_SDP_8B &&
            xdlh_xmlh_data[i*32 + 12 +: 4] == 4'b0010;
   end
end

assign valid_sdp = |(xdlh_xmlh_sdp & ~mask_sdp);


assign  or_sdp                  = |xdlh_xmlh_sdp;
assign  or_stp                  = |xdlh_xmlh_stp;
assign  or_eot                  = |(xdlh_xmlh_eot);
assign  int_xdlh_pad            = |int_link_mode[4:3] ? xdlh_xmlh_pad : {NW{1'b0}};


// pcie_perf
assign eot_is_last = (xdlh_xmlh_stp < xdlh_xmlh_eot) & (xdlh_xmlh_sdp < xdlh_xmlh_eot);
assign next_eot_is_last = (next_xdlh_xmlh_eot > next_xdlh_xmlh_stp) & (next_xdlh_xmlh_eot > next_xdlh_xmlh_sdp);
assign sot_is_last = (xdlh_xmlh_stp > xdlh_xmlh_eot);
assign sdp_is_last = (xdlh_xmlh_sdp > xdlh_xmlh_eot);

always @(posedge core_clk or negedge core_rst_n)
begin : eidle_sent_cmd_proc
    if (!core_rst_n)
        latched_eidle_sent_cmd      <= #TP 1'b0;
    else if ((xmtbyte_eidle_sent & ~(ltssm_cmd == `EPX16_SEND_EIDLE)) || (ltssm_cmd == `EPX16_XMT_IN_EIDLE))
        latched_eidle_sent_cmd      <= #TP 1'b0;
    else if (ltssm_cmd == `EPX16_SEND_EIDLE)
        latched_eidle_sent_cmd      <= #TP 1'b1;
end

// Latch xmlh_xmlh_pkt_pending condition.
// Only latched when ltssm_cmd == NORM. This ensures that LTSSM was
// transmitting data when xdlh_xmlh_pkt_pending was asserted.
// Latched condition is used to flush pipeline of registered packets
// when ltssm_cmd changes from NORM
always @(posedge core_clk or negedge core_rst_n)
begin : xdlh_pkt_pending_proc
    if (!core_rst_n)
        latched_pkt_pending <= #TP 1'b0;
    else if (((ltssm_cmd == `EPX16_NORM) || cmd_is_data) && xdlh_xmlh_pkt_pending)
        latched_pkt_pending <= #TP 1'b1;
    else if ( ((ltssm_cmd != `EPX16_NORM) && !xdlh_xmlh_pkt_pending) || (xbyte_curnt_state == S_EIDLE) || (ltssm_cmd == `EPX16_XMT_IN_EIDLE) )      
        latched_pkt_pending <= #TP 1'b0;
end

always @(posedge core_clk or negedge core_rst_n)
begin
    if (!core_rst_n)
        latched_in_mod_compliance <= #TP 1'b0;
    else if ((xbyte_next_state == S_CMP) && (xbyte_curnt_state != S_CMP))
        latched_in_mod_compliance <= #TP (ltssm_cmd == `EPX16_MOD_COMPL_PATTERN);
    else if ((xbyte_next_state != S_CMP) && (xbyte_curnt_state == S_CMP))
        latched_in_mod_compliance <= #TP 1'b0;
end

// 1cycle delayed next_cmd and next_pat
always @(posedge core_clk or negedge core_rst_n)
begin
    if (!core_rst_n) begin
        next_cmd_d <= #TP 0;
        next_pat_d <= #TP 0;
    end else begin
        next_cmd_d <= #TP next_cmd;
        next_pat_d <= #TP next_pat;
    end
end

// figure out the next command here so we don't have to do it in each state of the state machine
reg first_g5_eieos_sent;

always @(*)
begin : next_cmd_proc
    // send 2 consecutive eieos for gen5 rate
    // in Recovery.RcvrLock state, if pending packets (back-to-back packets may be from retry buffer), do not send the first EIEOS until the pending packets complete
    if ( next_cmd_d == S_EIES && ((~first_g5_eieos_sent && (
                                ~((ltssm_cmd != `EPX16_NORM) && xdlh_xmlh_pkt_pending && latched_pkt_pending))) || (first_g5_eieos_sent && ~xmtbyte_eies_sent))
 && current_data_rate >= `EPX16_GEN5_RATE) begin
        next_cmd   = next_cmd_d;
        next_pat   = next_pat_d;
    end else
    if (ltssm_ts_halt_next) begin
    // to avoid transmitting SKP OS after SDS OS without EDS when the current data rate is GEN3 and the LTSSM is in 
    // Recovery.Idle. If the transmitting txdata is not valid, next_cmd and next_pat keeps the previous value.
    // to avoid transmitting SKP OS after EDS when the current data rate is GEN3 or above and the LTSSM moves from L0 to Recovery.Lock.
    // When exiting a Data Stream, DUT must transmit an EDS token followed by an EIOS or an EIEOS.  
        next_cmd   = next_cmd_d;
        next_pat   = next_pat_d;
    end else
    if ((skip_req && (ltssm_cmd != `EPX16_XMT_IN_EIDLE)
                 && (ltssm_cmd != `EPX16_SEND_N_FTS)
                 && (ltssm_cmd != `EPX16_SEND_EIDLE)
                 && (ltssm_cmd != `EPX16_SEND_RCVR_DETECT_SEQ))
                 ) begin
        next_cmd   =  S_SKIP;
        next_pat   = `EPX16_SEND_SKP;
    // since the interface handshake between this block and ltssm block is
    // asynchronious, we use edge detect to identify the next eidle send command
    end
    // If EDS was inserted due to SKP, ensure SKP is sent after the EDS
    // This is in case ltssm_cmd has changed to `EPX16_SEND_EIDLE while EDS was
    // inserted
    else if (skp_after_eds) begin
        next_cmd   =  S_SKIP;
        next_pat   = `EPX16_SEND_SKP;
    end
    // At 5.0 GT/s, send 8 EIE Symbols prior to sending first FTS.
    // If N_FTS==0, send 8 EIE Symbols before sending SKP
    else if (cmd_is_fts_send && (current_data_rate == `EPX16_GEN2_RATE) && !latched_eies_sent_sym) begin
        next_cmd   = S_EIES_SYM;
        next_pat   = `EPX16_SEND_EIES_SYM;
    end
    // At 2.5 GT/s and 5.0 GT/s, send a SKP OS at the end of the FTS sequnce
    else if ((int_xmt_nfts_done && cmd_is_fts_send && !cfg_ext_synch && (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE)) ||
             (fts_skip_req && (curnt_is_short || (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE))) ||
             (latched_fts_skip_req && !curnt_is_short && (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE))) begin
        next_cmd   =  S_SKIP;
        next_pat   = `EPX16_SEND_SKP;
    end
    // Transmit SKPs first if SKPs are requested during transmitting remaining registered packers from XDLH.
    // This happens when LTSSM moves to low power state and ltssm_cmd = `EPX16_SEND_EIDLE for Gen3 config. In this
    // case if skp is requested to send and framed pending pkts is requested to send, send skip first,
    // then pending pkts, finally EIOS.
    // doing so is to avoid pkt format change after speed change from exiting of low power.
    // for gen1/2 config, even though after speed change, the pkt format is the same. So we don't need to send pending
    // pkts before moving into low power state, instead sending EIOS immediately.
// ccx_line_begin: ; unreachable because no packet data is on TX pipeline when PM block directs ltssm_cmd = ~NORM in S_L123_SEND_EIDLE LTSSM state. For safety code here.
    else if ( ( ((ltssm_cmd != `EPX16_NORM) && xdlh_xmlh_pkt_pending && latched_pkt_pending)) && skip_req ) begin
        next_cmd   =  S_SKIP;
        next_pat   = `EPX16_SEND_SKP;
    end
// ccx_line_end
    // Transmit remaining registered packers from XDLH
    else if ( ((ltssm_cmd != `EPX16_NORM) && xdlh_xmlh_pkt_pending && latched_pkt_pending) ) begin
        if (latched_sds_req) begin //if SDS pending, send SDS first
            next_cmd   = S_SDS;
            next_pat   = `EPX16_SEND_SDS;
        end else begin
            next_cmd   =  S_XPKT_WAIT4_START;
            next_pat   = `EPX16_SEND_IDLE;
        end
    end
    else if (((cxl_mode_enable && ltssm_cxl_sh_bypass) ? ((((ltssm_cmd == `EPX16_SEND_EIDLE) || latched_eidle_sent_cmd) & pid_count == 0) | eid_win_i | eid_win_d) : ((ltssm_cmd == `EPX16_SEND_EIDLE) || latched_eidle_sent_cmd)) && (!int_eidle | xmtbyte_eidle_sent) && (!skp_win || !skp_win_d)) begin
        next_cmd   =  S_EIDLE;
        next_pat   = `EPX16_SEND_EIDLE;
    end
    // Periodic EIEOS Insertion during TS1/TS2/FTS at certain data rates.
    else if ((((cxl_mode_enable && ltssm_cxl_sh_bypass) ? ((eieos_required & pid_count == 0 & ~pkt_in_progress) | eie_win_i | eie_win_d) : eieos_required) && (!latched_eieos_sent || ltssm_eq_rlock) && (ltssm_cmd != `EPX16_XMT_IN_EIDLE) && (!skp_win || !skp_win_d))
      ) begin
        next_cmd   = S_EIES;
        next_pat   = `EPX16_SEND_EIES;
    end
    // EIEOS Insertion at end of FTS sequence at 8 GT/s
    else if (fts_eieos_req) begin
        next_cmd   = S_EIES;
        next_pat   = `EPX16_SEND_EIES;
    end
    else if (xmtfts_req) begin
        next_cmd   =  S_FTS;
        next_pat   = `EPX16_SEND_N_FTS;
    end
    else if ( ltssm_cmd == `EPX16_SEND_TS1 || ltssm_cmd == `EPX16_SEND_TS2 ) begin
        begin
            next_cmd   =  S_TS;
            next_pat   = ltssm_cmd;
        end
    end
    else if (ltssm_cmd == `EPX16_COMPLIANCE_PATTERN) begin
        next_cmd   =  S_CMP;
        next_pat   = `EPX16_COMPLIANCE_PATTERN;
    end
    else if (ltssm_cmd == `EPX16_MOD_COMPL_PATTERN) begin
        next_cmd   =  S_CMP;
        next_pat   = `EPX16_MOD_COMPL_PATTERN;
    end
    else if (latched_g3_lpbk_eies_req) begin
        next_cmd   = S_EIES;
        next_pat   = `EPX16_SEND_EIES;
    end
    // Transmit the enhanced SKPOS when the LTSSM enters the Configuration.Idle state or Recovery.Idle state
    else if (latched_ctlskp_req[0]) begin
        next_cmd   =  S_SKIP;
        next_pat   = `EPX16_SEND_SKP;
    end
    else if (latched_sds_req) begin
        next_cmd   = S_SDS;
        next_pat   = `EPX16_SEND_SDS;
    end
    else if (ltssm_cmd == `EPX16_NORM || (((ltssm_cmd == `EPX16_SEND_IDLE) || (ltssm_cmd == `EPX16_SEND_EIDLE)) && (cxl_mode_enable))) begin
        next_cmd   =  S_XPKT_WAIT4_START;
        next_pat   = `EPX16_SEND_IDLE;
    end
    else begin
        next_cmd   =  S_IDLE;
        next_pat   = `EPX16_SEND_IDLE;
    end
end

assign state_halt  = txdata_dv0_required; // Dont update state when txdata_dv is about to be de-asserted
assign  data_halt  = 1'b0;

// synchronous state machine
always @(posedge core_clk or negedge core_rst_n)
begin : curnt_state_proc
    if (!core_rst_n) begin
        xbyte_curnt_state   <= #TP S_IDLE;


    end else if ( !state_halt ) begin
        xbyte_curnt_state   <= #TP xbyte_next_state;
    end
end


always @(*)
begin : next_state_proc
    begin
        case (xbyte_curnt_state)
        S_IDLE : begin
            if (eds_required)
                xbyte_next_state   =  S_EDS;
            else
                xbyte_next_state   =  next_cmd;
        end

        S_FTS :
            // SKP always sent at end of FTS for Gen1/Gen2
            if (fts_skip_req && (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE))
                xbyte_next_state   =  S_SKIP;
            // SKP not sent at end of FTS for Gen3, SDS sent instead
            else if (os_end)
                xbyte_next_state   = next_cmd;
            else
                xbyte_next_state   =  S_FTS;

        S_SKIP :
            if ( (os_end || (short_os_4s && curnt_is_short))
                 && !eds_required
                 && !skip_req )
                xbyte_next_state   =  next_cmd;
            //  When using 128b/130b encoding, SKP Ordered Sets cannot be
            //  transmitted in consecutive Blocks within a Data Stream.
            //  Transmitting a SKP Ordered Set followed immediately by another
            //  Ordered Set Block (including another SKP Ordered Set) within a
            //  Data Stream is a Framing Error.
            else if (os_end && eds_required && !eds_inserted_4_skp && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE))
                   xbyte_next_state   = S_EDS;
            else
                xbyte_next_state   = S_SKIP;

        S_EIDLE :
            if (os_end)
                xbyte_next_state   = next_cmd;
            else
                xbyte_next_state   = S_EIDLE;

        S_TS :
            if (os_end)
                xbyte_next_state   = next_cmd;
            else
                xbyte_next_state   = S_TS;

        S_EIES :
            if (os_end)
                xbyte_next_state   = next_cmd;
            else
                xbyte_next_state   = S_EIES;

        S_EIES_SYM : // This is always followed by FTSs when nfts != 0
            if (os_end) begin
                // At 5 GT/s, if N_FTS==0, send a SKP instead of entering S_FTS
                // Otherwise, enter S_FTS and send FTS
                if ((latched_ts_nfts == 8'b0) && (current_data_rate == `EPX16_GEN2_RATE))
                    xbyte_next_state   = S_SKIP;
                else
                    xbyte_next_state   = S_FTS;
            end
            else
                xbyte_next_state   = S_EIES_SYM;
        S_SDS :
            if (os_end && eds_required)
                xbyte_next_state   = S_EDS;
            else if (os_end)
                xbyte_next_state   = next_cmd;
            else
                xbyte_next_state   = S_SDS;

        S_EDS :
            if (eds_inserted) // EDS token has been inserted
                xbyte_next_state   = next_cmd;
            else
                xbyte_next_state   = S_EDS;

        S_CMP :
            if ( os_end )
                if ( cfg_compliance_sos && skip_req )
                    xbyte_next_state       =  S_SKIP;
                else
                if ( (ltssm_cmd != `EPX16_COMPLIANCE_PATTERN) && (ltssm_cmd != `EPX16_MOD_COMPL_PATTERN) )
                    if (ltssm_ts_halt)
                        xbyte_next_state   =  next_cmd;
                    else
                    if (current_data_rate == `EPX16_GEN2_RATE || current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE)
                        xbyte_next_state   =  S_EIDLE;
                    else
                        xbyte_next_state   =  next_cmd;
                else
                    xbyte_next_state   =  S_CMP;
            else
                xbyte_next_state   =  S_CMP;

        S_XPKT_WAIT4_START :
/*
  `ifdef EPX16_CX_FLIT_MODE_ENABLE
            if ( insert_eid_for_flit ) begin
            // if insert eios for flit, do it immediately
                xbyte_next_state = S_EIDLE;
            end else if ( insert_eie_for_flit ) begin
            // if insert eieos for flit, do it immediately
                xbyte_next_state = S_EIES;
            end else if ( insert_skp_for_flit ) begin
            // if insert skp for flit, do it immediately
                xbyte_next_state = S_SKIP;
            end else
  `endif // CX_FLIT_MODE_ENABLE
*/
// perf_enh

            if ( nw_gtr_2 && ( (sdp_is_last || sot_is_last)  || ((or_sdp || or_stp) && !(eot_is_last & !xmtbyte_xdlh_halt)))  && !(skip_req || skip_insert) ||
       //      if ( nw_gtr_2 && ((sdp_is_last || sot_is_last) && !xmtbyte_xdlh_halt && !(skip_req || skip_insert)) ||
           !nw_gtr_2 && ((or_sdp || or_stp) && !(or_eot && !xmtbyte_xdlh_halt) &&  !(skip_req || skip_insert)) )

                              // wait for the end unless we can handle this pkt in the current cycle
                             // Do no start pkt if SKP is pending transmission
                xbyte_next_state   =  S_XPKT_WAIT4_STOP;
            else if ((xdlh_xmlh_pkt_pending || latched_pkt_pending) && !(skip_req || skip_insert))
                xbyte_next_state   =  S_XPKT_WAIT4_START;               // Do not insert EDS until pending pkts are transmitted (except in case of SKP)
            else if (eds_required)
                xbyte_next_state   =  S_EDS;
            else if  (!valid_pkt_in_progress_i | valid_pkt_in_progress_i & !xmtbyte_xdlh_halt)
                xbyte_next_state   =  next_cmd;
       else
           xbyte_next_state   =  S_XPKT_WAIT4_START;

        S_XPKT_WAIT4_STOP :
/*
  `ifdef EPX16_CX_FLIT_MODE_ENABLE
       if ( insert_eid_for_flit ) begin
       // if insert eios for flit, do it immediately
           xbyte_next_state = S_EIDLE;
       end else if ( insert_eie_for_flit ) begin
       // if insert eieos for flit, do it immediately
           xbyte_next_state = S_EIES;
       end else if ( insert_skp_for_flit ) begin
       // if insert skp for flit, do it immediately
           xbyte_next_state = S_SKIP;
       end else
  `endif // CX_FLIT_MODE_ENABLE
*/
 // perf_enh

       if  ( (nw_gtr_2 && eot_is_last || !nw_gtr_2 && or_eot) &&

                !eds_required    &&
                !xmtbyte_xdlh_halt )     // wait for the end of the pkt
/*
              `ifdef EPX16_CX_FLIT_MODE_ENABLE
                if ( ltssm_flit_mode_enable )
                    xbyte_next_state   =  S_XPKT_WAIT4_STOP;
                else
              `endif // CX_FLIT_MODE_ENABLE
*/
                xbyte_next_state   =  next_cmd;
// perf_enh

            else if ( (nw_gtr_2 && eot_is_last && !xmtbyte_xdlh_halt && (xdlh_xmlh_pkt_pending || latched_pkt_pending) && !(skip_req || skip_insert)) ||
                 (!nw_gtr_2 && or_eot && !xmtbyte_xdlh_halt && (xdlh_xmlh_pkt_pending || latched_pkt_pending) && !(skip_req || skip_insert)) )
                       xbyte_next_state   =  S_XPKT_WAIT4_START;           // Do not insert EDS until pending pkts are transmitted (except in case of SKP)
// perf_enh
            else if ( (nw_gtr_2 && (eot_is_last || latched_flit_null_d) && !xmtbyte_xdlh_halt && eds_required ) ||  // End of Pkt and EDS Token is required
                      (!nw_gtr_2 && (or_eot || latched_flit_null_d) && !xmtbyte_xdlh_halt && eds_required) ) // End of Pkt and EDS Token is required
                       xbyte_next_state   =  S_EDS;

            else
                xbyte_next_state   =  S_XPKT_WAIT4_STOP;


        default :
            xbyte_next_state   =  S_IDLE;

        endcase
    end
end

// FTS support logic
wire                int_ext_synch_done;
assign  cmd_is_fts_send      = (ltssm_cmd == `EPX16_SEND_N_FTS);
assign  int_xmt_nfts_done    = ( cmd_is_fts_send && ((fts_sent_cnt[7:0] == latched_ts_nfts) || latched_int_xmt_nfts_done) );
assign  int_ext_synch_done   = cmd_is_fts_send && cfg_ext_synch && fts_sent_cnt[12];
assign  xmtbyte_fts_sent     = ( (int_xmt_nfts_done && !cfg_ext_synch) || (int_ext_synch_done && cfg_ext_synch) );
// Sending SKP during SEND_N_FTS Command
// 1. A single SKP is sent after N_FTS FTSs at 2.5 GT/s and 5 GT/s data rates when Extended Synch bit is not set
// 2. A single SKP is sent after 4096 FTSs  at 2.5 GT/s and 5 GT/s data rates when Extended Synch bit is set
// 3. SKP must not be transmitted during first N_FTS FTSs after which SKP must be scheduled and transmitted between
//    FTS and/or EIEOS as necessary to meet definitions in "Clock Tolerance Compensation" section in Base Specification.
//    This applies at all data rates
assign  fts_skip_req         = (cmd_is_fts_send && int_xmt_nfts_done && (
                                   (!cfg_ext_synch && (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE)) ||
                                   (int_ext_synch_done && cfg_ext_synch && (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE)) ||
                                   (skip_req && cfg_ext_synch)));
assign fts_eieos_req        =  ((xmtbyte_fts_sent || latched_fts_eieos_req) && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE));

// latch int_xmt_nfts_done during extended sync
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        latched_int_xmt_nfts_done   <= #TP 1'b0;
    else if (xmtbyte_fts_sent)
        latched_int_xmt_nfts_done   <= #TP 1'b0;
    else if (int_xmt_nfts_done && cfg_ext_synch)
        latched_int_xmt_nfts_done   <= #TP 1'b1;

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        latched_fts_skip_req    <= #TP 1'b0;
    else if ( (next_pat == `EPX16_SEND_SKP) && load_pat )    // clear after the pattern has been loaded
        latched_fts_skip_req    <= #TP 1'b0;
    else if (( (xbyte_curnt_state == S_FTS) && fts_skip_req )
            || ( (xbyte_curnt_state == S_EIES_SYM) && (latched_ts_nfts == 8'b0) && (current_data_rate == `EPX16_GEN2_RATE))
            )
        latched_fts_skip_req    <= #TP 1'b1;

// Send an EIEOS after FTS Sequence completes at 8 GT/s
// Note: If N_FTS is a multiple of 32, then the EIEOS
// counter also ensures that an EIEOS is sent after FTS Sequnece.
// IF N_FTS is not a multiple of 32, then this logic ensures EIEOS is sent.
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        latched_fts_eieos_req    <= #TP 1'b0;
    else if ( (next_pat == `EPX16_SEND_EIES) && load_pat )
        latched_fts_eieos_req    <= #TP 1'b0;
    else if (xmtbyte_fts_sent && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE))
        latched_fts_eieos_req    <= #TP 1'b1;

// for Gen3 Loopback from Loopback.Entry -> Loopback.Active transition,
// an EIEOS needs to be sent prior to SDS
always @(posedge core_clk or negedge core_rst_n) begin : latched_g3_lpbk_eies_req_PROC
    if (!core_rst_n)
        latched_g3_lpbk_eies_req <= #TP 1'b0;
    else if ( ltssm_lpbk_master && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) && eieos_req_cnt == 31 && xbyte_next_state == S_EIES && smlh_ltssm_state ==  `EPX16_S_LPBK_ACTIVE && ~(skip_insert || skip_req) )
        latched_g3_lpbk_eies_req <= #TP 1'b0;
    else if ((next_pat == `EPX16_SEND_TS1) && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) && ltssm_lpbk_master && ((ltssm_in_lpbk && ~eieos_required) || ltssm_in_lpbkentry))
        latched_g3_lpbk_eies_req <= #TP 1'b1;
    else if ( ((next_pat == `EPX16_SEND_EIES) && load_pat) || !(ltssm_in_lpbk || ltssm_in_lpbkentry) )
        latched_g3_lpbk_eies_req <= #TP 1'b0;
end

// Conditions for SDS Insertion:
// 1. Send an SDS after FTS Sequnce completes at 8 GT/s.
//    SDS request is latched and follows EIEOS at end of FTS sequence.
// 2. Send an SDS prior to transmitting IDL during Recovery.Idle or
//    Configuration.Idle.
//    SDS request is latched and sent prior to entering S_IDLE.
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        latched_sds_req    <= #TP 1'b0;
    else if ( smlh_ltssm_state == `EPX16_S_LPBK_EXIT || smlh_ltssm_state == `EPX16_S_LPBK_EXIT_TIMEOUT || current_data_rate <= `EPX16_GEN2_RATE)
        latched_sds_req    <= #TP 1'b0; // no tx sds in S_LPBK_EXIT or S_LPBK_EXIT_TIMEOUT
    else if (ltssm_in_lpbk || ltssm_in_lpbkentry) begin
        if (ltssm_in_lpbkentry || !ltssm_lpbk_master) //clear latched_sds_req when enter Loopback.Entry
            latched_sds_req    <= #TP 1'b0; //no SDS transmit for Loopback Slave
        else if (cmd_is_pat && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) && ltssm_lpbk_master)
            latched_sds_req    <= #TP 1'b1;
        else if ((next_pat == `EPX16_SEND_SDS) && load_pat)
            latched_sds_req    <= #TP 1'b0;
    end else if (ltssm_in_compliance || (next_pat == `EPX16_SEND_EIDLE)) //no SDS required in Polling.Compliance or after EIOS
        latched_sds_req    <= #TP 1'b0;
    else if (cmd_is_pat && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE))
        latched_sds_req    <= #TP 1'b1;
    else if ( (next_pat == `EPX16_SEND_SDS) && load_pat)
        latched_sds_req    <= #TP 1'b0;


// Conditions for Control SKP Insertion:
// 1. The data rate is only 16.0 GT/s.
// 2. Send an Control SKP followed immediately by one SDS during Recovery.Idle or Configuration.Idle.
always @(posedge core_clk or negedge core_rst_n) begin
    if (!core_rst_n) begin
        latched_ctlskp_req    <= #TP 2'b00;
    end else if (ltssm_in_lpbk || ltssm_in_lpbkentry) begin
        latched_ctlskp_req    <= #TP 2'b00;
    //no enhanced SKP-OSrequired in Polling.Compliance or after EIOS or FTSOS after L0s.Exit
    end else if (ltssm_in_compliance || (next_pat == `EPX16_SEND_EIDLE) || cmd_is_fts_send ) begin
        latched_ctlskp_req    <= #TP 2'b00;
    end else if (cmd_is_pat && ltssm_in_rcvry_cfg_idle && (current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE)) begin
        latched_ctlskp_req    <= #TP 2'b01;

    end else if ( (next_pat == `EPX16_SEND_SKP) && next_ctlskip_pat && load_pat && latched_ctlskp_req[0]) begin
        latched_ctlskp_req    <= #TP 2'b10;
    end else if ( in_data_stream ) begin
        latched_ctlskp_req    <= #TP 2'b00;
    end
end

// SKPOS transmitted within a Data Stream must alternate between the standard format and the status reporting format at gen4 rate.
always @(posedge core_clk or negedge core_rst_n) begin
    if (!core_rst_n) begin
        in_data_stream       <= #TP 1'b0;
        next_ctlskip_pat_req <= #TP 1'b0;
    end else if ((current_data_rate != `EPX16_GEN4_RATE && current_data_rate != `EPX16_GEN5_RATE) || ltssm_in_lpbk || ltssm_in_compliance || (latched_ctlskp_req[0])) begin
        in_data_stream       <= #TP 1'b0;
        next_ctlskip_pat_req <= #TP 1'b0;
    end else begin
        in_data_stream       <= #TP ((next_pat == `EPX16_SEND_SDS) && load_pat) ? 1'b1 :
                                   (((next_pat == `EPX16_SEND_EIDLE) || (next_pat == `EPX16_SEND_EIES)) && load_pat) ? 1'b0 : in_data_stream;
        next_ctlskip_pat_req <= #TP
                                   (~in_data_stream) ? 1'b0 :
                                    ((next_pat == `EPX16_SEND_SKP) && load_pat) ? ~next_ctlskip_pat_req : next_ctlskip_pat_req;
    end
end

assign next_ctlskip_pat = (latched_ctlskp_req[0] && (ltssm_cmd == `EPX16_SEND_IDLE)) || next_ctlskip_pat_req;

// Generate enhanced skip-os signal
always @(posedge core_clk or negedge core_rst_n) begin : in_ctlskip_d_PROC
    if (!core_rst_n) begin
        xmlh_pat_in_ctlskip_d  <= #TP 1'b0;
        xmtbyte_ctlskip_sent_d <= #TP 1'b0;
    end else begin
        xmlh_pat_in_ctlskip_d  <= #TP |xmlh_pat_in_ctlskip;
        xmtbyte_ctlskip_sent_d <= #TP xmtbyte_ctlskip_sent;
    end
end

assign xmtbyte_ctlskip_sent = (|xmlh_pat_in_ctlskip && ~xmlh_pat_in_ctlskip_d);

always @(posedge core_clk or negedge core_rst_n) begin : int_lpbkentry_d_PROC
    if (!core_rst_n)
        int_lpbkentry_d <= #TP 1'b0;
    else
        int_lpbkentry_d <= #TP ltssm_in_lpbkentry;
end

assign int_lpbkentry_pulse = ltssm_in_lpbkentry && ~int_lpbkentry_d;



always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n) begin
         fts_sent_cnt    <= #TP 13'h0000;
    end else if (xmtbyte_fts_sent || ((xbyte_curnt_state != S_FTS)
                               && (xbyte_curnt_state != S_EIES)
                               && (xbyte_curnt_state != S_SKIP)) ) begin
         fts_sent_cnt    <= #TP 13'h0000;
    end else if (((xbyte_curnt_state == S_FTS)  ||
              (xbyte_curnt_state == S_EIES) ||
              (xbyte_curnt_state == S_SKIP)) && xmlh_pat_fts_sent) begin
         fts_sent_cnt    <= #TP fts_sent_cnt + 13'h1;
    end

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
         dlyd_cmd_is_fts_send    <= #TP 1'b0;
    else
         dlyd_cmd_is_fts_send    <= #TP cmd_is_fts_send;

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
         latched_eies_sent_sym   <= #TP 1'b0;
    else if (xbyte_curnt_state == S_EIES_SYM)
         latched_eies_sent_sym   <= #TP 1'b1;
    else if (ltssm_cmd != `EPX16_SEND_N_FTS)
         latched_eies_sent_sym   <= #TP 1'b0;

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
         latched_eieos_sent   <= #TP 1'b0;
//    else if (ltssm_cmd_send_eieos_for_pset_map && ~ltssm_usp_eq_redo)
    else if (ltssm_cmd_send_eieos_for_pset_map)
         latched_eieos_sent   <= #TP 1'b0;
    else if (!eieos_required || ltssm_eq_rlock )
         latched_eieos_sent   <= #TP 1'b0;
    else if (((next_pat == `EPX16_SEND_EIES) && load_pat) || xmtbyte_eies_sent) // EIEOS Pattern Loaded
         latched_eieos_sent   <= #TP 1'b1;



reg     [2:0]           eidle_sent_cnt;

always @(posedge core_clk or negedge core_rst_n)
begin : eidle_sent_cnt_proc
    if(!core_rst_n )
         eidle_sent_cnt    <= #TP 3'b0;
    else if (ltssm_cmd != `EPX16_SEND_EIDLE)
         eidle_sent_cnt    <= #TP 3'b0;
    else
// spyglass disable_block W164a
// SMD: Identifies assignments in which the LHS width is less than the RHS width
// SJ: The rule reports assignments where the result of an addition or subtraction operation is being assigned to a bus of the same width as the operands of the addition or subtraction operation. In this code, the carry or borrow bit is considered and isn't lost. So, disable SpyGlass from reporting this warning.
         eidle_sent_cnt    <= #TP eidle_sent_cnt + xmlh_pat_eidle_sent[0];
// spyglass enable_block W164a
end

reg     [4:0]           eidle_sent_cnt_mod;
reg     [4:0]           ltssm_eidle_cnt_mod;

always @(posedge core_clk or negedge core_rst_n)
begin : eidle_sent_cnt_mod_proc
    if(!core_rst_n )
         eidle_sent_cnt_mod    <= #TP 0;
    else if (ltssm_cmd != `EPX16_SEND_EIDLE)
         eidle_sent_cnt_mod    <= #TP 0;
    else
// spyglass disable_block W164a
// SMD: Identifies assignments in which the LHS width is less than the RHS width
// SJ: The rule reports assignments where the result of an addition or subtraction operation is being assigned to a bus of the same width as the operands of the addition or subtraction operation. In this code, the carry or borrow bit is considered and isn't lost. So, disable SpyGlass from reporting this warning.
         eidle_sent_cnt_mod    <= #TP eidle_sent_cnt_mod + xmlh_pat_eidle_sent[0];
// spyglass enable_block W164a
end


// cdm_ras_des_eidle_cnt_sel
// number of transmitting EIOSs
// 2.5GT/s
// 00: 1/ 01: 4/ 10: 8/ 11: 16
// 5.0GT/s
// 00: 2/ 01: 8/ 10: 16/ 11: 32
// 8.0GT/s
// 00: 1/ 01: 4/ 10: 8/ 11: 16
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        ltssm_eidle_cnt_mod <= #TP 0;
    else begin
        case(cdm_ras_des_eidle_cnt_sel)
            'b00: ltssm_eidle_cnt_mod <= #TP {2'b0, ltssm_eidle_cnt};
            'b01: begin
                if(ltssm_eidle_cnt==7) 
                    ltssm_eidle_cnt_mod <= #TP 7;
                else begin
                    ltssm_eidle_cnt_mod <= #TP (current_data_rate == `EPX16_GEN2_RATE) ?  7 : 3 ;
                end
            end
            'b10: ltssm_eidle_cnt_mod <= #TP (current_data_rate == `EPX16_GEN2_RATE) ?  15 : 7 ;
            'b11: ltssm_eidle_cnt_mod <= #TP (current_data_rate == `EPX16_GEN2_RATE) ?  31 : 15 ;
        endcase
    end

assign pre_xmtbyte_eidle_sent[0] = xmlh_pat_eidle_sent[0]                                                 && (eidle_sent_cnt_mod == ltssm_eidle_cnt_mod)
                                                ;
assign xmtbyte_eidle_sent = |pre_xmtbyte_eidle_sent;

always @(posedge core_clk or negedge core_rst_n)
begin : xmtfts_req_proc
    if (!core_rst_n)
        xmtfts_req  <= #TP 0;
    else
      // detected the assertion edge of SEND NFTS command to start the
      // fts transmit process. Until all fts sent, we should not
      // send any other sequence.
        xmtfts_req  <= #TP xmtbyte_fts_sent ? 1'b0
                           : (cmd_is_fts_send & !dlyd_cmd_is_fts_send)  ? 1'b1
                           : xmtfts_req;
end

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        curnt_compliance_finished       <= #TP 1'b0;
    else
        curnt_compliance_finished       <= #TP (xbyte_curnt_state != S_CMP);


wire  int_in_training;
assign  int_in_training = (xbyte_curnt_state == S_CMP)
                          || (xbyte_curnt_state == S_EIES)
                          || (xbyte_curnt_state == S_TS);

// Receiver Detect / Loopback signaling
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        xmtbyte_txdetectrx_loopback <= #TP 0;
    else
        // According to PIPE, loopback was indicated to PHY as overload of txdetectrx signal
        xmtbyte_txdetectrx_loopback <= #TP (  (ltssm_cmd == `EPX16_SEND_RCVR_DETECT_SEQ) // for signalling receiver detect
                                            | (ltssm_in_lpbk_i && !lpbk_master && (     // for signalling loopback
 (ltssm_cmd == `EPX16_XMT_IN_EIDLE && current_data_rate >= `EPX16_GEN5_RATE) ? 1'b1 : // ltssm_cmd == XMT_IN_EIDLE in ltssm_in_lpbk_i means Loopback.Active from Loopback EQ
                                                &(~ltssm_lanes_active | (ltssm_lanes_active & ~xmtbyte_txelecidle)))) ); // active lanes aren't in eidle


// Electrical Idle signaling
always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        goto_xmt_eidle              <= #TP 1'b0;
    else
        if (ltssm_cmd == `EPX16_SEND_EIDLE)
            goto_xmt_eidle              <= #TP 1'b1;
        else if ((ltssm_cmd != `EPX16_SEND_EIDLE) && (ltssm_cmd != `EPX16_XMT_IN_EIDLE) && (ltssm_cmd != `EPX16_SEND_RCVR_DETECT_SEQ))
            goto_xmt_eidle              <= #TP 1'b0;
        else
            goto_xmt_eidle              <= #TP goto_xmt_eidle;

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n) begin
        xmtbyte_eidle_sent_d        <= #TP 1'b0;
        xmtbyte_skip_sent_d         <= #TP 1'b0;
        xmtbyte_eies_sent_d         <= #TP 1'b0;
    end else begin
        xmtbyte_eidle_sent_d        <= #TP xmtbyte_eidle_sent;
        xmtbyte_skip_sent_d         <= #TP xmtbyte_skip_sent;
        xmtbyte_eies_sent_d         <= #TP xmtbyte_eies_sent;
    end

wire os_start_needed = (current_data_rate > `EPX16_GEN2_RATE) ? os_start : 1'b1; // TxCmpl=1 in Polling.Compliance is only for Gen1/2 rate. So for >gen2_rate it is safe to use os_start.


assign int_eidle_i = (xmtbyte_eidle_sent_d || (ltssm_cmd == `EPX16_XMT_IN_EIDLE) || (ltssm_cmd == `EPX16_SEND_RCVR_DETECT_SEQ)) ? 1'b1 :
        ((ltssm_cmd != `EPX16_SEND_EIDLE) && (ltssm_cmd != `EPX16_XMT_IN_EIDLE) && (ltssm_cmd != `EPX16_SEND_RCVR_DETECT_SEQ)
                 && (|xmtbyte_ts1_sent || |xmtbyte_ts2_sent || (os_start_needed && ((ltssm_cmd == `EPX16_COMPLIANCE_PATTERN) || (ltssm_cmd == `EPX16_MOD_COMPL_PATTERN))) // immediately no TxEIdle if sending COMPLIANCE_PATTERN to avoid TxEIdle=1 & TxCmpl=1 on all lanes
                    || xmtbyte_eies_sent
                    || xmtbyte_skip_sent
                    || xmlh_pat_fts_sent
                    || ltssm_in_lpbk)) ? 1'b0 : int_eidle;

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        int_eidle                   <= #TP 1'b1;
    else
        int_eidle                   <= #TP int_eidle_i;

assign os_sent_d      = xmtbyte_skip_sent_d | xmtbyte_ctlskip_sent_d | xmtbyte_eies_sent_d | xmtbyte_eidle_sent_d;
assign skip_os_sent_d = xmtbyte_skip_sent_d | xmtbyte_ctlskip_sent_d;
wire   skip_os_sent   = xmtbyte_skip_sent | xmtbyte_ctlskip_sent;
assign os_sent        = skip_os_sent | xmtbyte_eies_sent | xmtbyte_eidle_sent;

always @(posedge core_clk or negedge core_rst_n) begin : ltssm_in_lpbk_d_PROC
    if ( ~core_rst_n )
        ltssm_in_lpbk_d <= #TP 1'b0;
    else
        ltssm_in_lpbk_d <= #TP ltssm_in_lpbk_i;
end // ltssm_in_lpbk_d_PROC

// when lanes that are not active, we will set it eidle
// for gen5 lpbk.active slave not sending Modified Compliance Pattern after EQ, the lane under test loops back data from rx to tx, the lanes not under test transitioned to TxElecIdle
assign xmtbyte_txelecidle_i = (ltssm_cmd == `EPX16_XMT_IN_EIDLE & ~lpbk_master & ltssm_in_lpbk && current_data_rate >= `EPX16_GEN5_RATE) ? (~ltssm_lpbk_slave_lut) : ( {NL{int_eidle}}  | (~(int_lanes_active)) | int_txelecile_g3_txdatavalid_sync);

wire xmtbyte_g5_eies_sent;
always @(posedge core_clk or negedge core_rst_n) begin
    if (!core_rst_n) begin
        int_lanes_active            <= #TP {NL{1'b1}};
    end else begin
        if ( (xbyte_curnt_state == S_IDLE) // Detect state / downsizing in gen1/gen2
                                            || (ltssm_in_lpbk && (lpbk_master || ~lpbk_master)) //for implementation-specific lanes or the lane under test from loopback eq
                                            || (xbyte_curnt_state == S_SDS) // downsizing in gen3/gen4
                                            || ((xbyte_curnt_state == S_SKIP) && xmlh_pat_in_ctlskip[0] && (ltssm_cmd == `EPX16_SEND_IDLE) && (current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE 
)) // downsizing in gen4
                                            || (xmtbyte_ts1_sent[0] && (current_data_rate == `EPX16_GEN1_RATE))  // upconfigure in gen1
                                            || (xmtbyte_g5_eies_sent && (current_data_rate != `EPX16_GEN1_RATE)) // upconfigure in gen2/gen3/gen4, use the back-back second eieos for >gen5
                                          ) begin
             int_lanes_active      <= #TP ltssm_lanes_active;
         end else begin
             int_lanes_active      <= #TP int_lanes_active;
         end
    end
end

assign int_lanes_active_for_data = int_lanes_active;


always @(posedge core_clk or negedge core_rst_n) begin
    if (!core_rst_n)
        int_txelecile_g3_txdatavalid_sync <= #TP {NL{1'b0}};
    else if( current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE ) begin
        if( xmtbyte_eies_sent && (ltssm_lanes_active != int_lanes_active) && xmtbyte_txdata_dv ) begin
            int_txelecile_g3_txdatavalid_sync <= #TP ~int_lanes_active;
        end
        else if( (!xmtbyte_txdata_dv)) begin
            int_txelecile_g3_txdatavalid_sync <= #TP {NL{1'b0}};
        end
    end
    else begin
        int_txelecile_g3_txdatavalid_sync <= #TP {NL{1'b0}};
    end
end

always @(posedge core_clk or negedge core_rst_n)
    if (!core_rst_n)
        int_link_mode               <= #TP 0;
    else
        int_link_mode               <= #TP ( (xbyte_curnt_state == S_IDLE) // Detect state / downsizing in gen1/gen2
                                            || (ltssm_in_lpbk && lpbk_master) //for implementation-specific lanes
                                            || (xbyte_curnt_state == S_SDS) // downsizing in gen3/gen4
                                            || (xmtbyte_ts1_sent[0] && (current_data_rate == `EPX16_GEN1_RATE))  // upconfigure in gen1
                                            || (xmtbyte_eies_sent && (current_data_rate != `EPX16_GEN1_RATE)) // upconfigure in gen2/gen3/gen4
                                          ) 
                                       ? smlh_link_mode : 
 // Updated link width for L0p
                                         int_link_mode;

assign  active_lane_cnt     =
                              (int_link_mode[4]) ? 5'd16 :
                              (int_link_mode[3]) ? 5'd8 :
                              (int_link_mode[2]) ? 5'd4 :
                              (int_link_mode[1]) ? 5'd2 :
                              (int_link_mode[0]) ? 5'd1 :
                                                    5'd0;

always @(posedge core_clk or negedge core_rst_n)
if (!core_rst_n) begin
    xmtbyte_idle_sent   <= #TP 1'b0;
end else begin
    xmtbyte_idle_sent   <= #TP (xbyte_curnt_state == S_IDLE);
end

always @(posedge core_clk or negedge core_rst_n)
if (!core_rst_n)
    cmp_dly_lane       <= #TP 1;
else
    if ( (xbyte_next_state == S_CMP) && ((xbyte_curnt_state != S_CMP) && (xbyte_curnt_state != S_SKIP)) ) // when we are about to start sending compliance
        cmp_dly_lane       <= #TP 1;
//    else if ( ((xbyte_next_state == S_CMP) || ((xbyte_next_state == S_SKIP) && xbyte_curnt_state == S_CMP)) && ((cmp_dly_lane[7]) && os_end) )   // Or we need to wrap back to lane zero
    else if ( (xbyte_curnt_state == S_CMP) && cmp_dly_lane[7] && os_end )   // Or we need to wrap back to lane zero
        cmp_dly_lane       <= #TP 1;
    else if ( (xbyte_curnt_state == S_CMP) && os_end )              // Its time to switch delayed lanes
        cmp_dly_lane       <= #TP cmp_dly_lane << 1;
// leda W565 on


// =============================================================================
// Skip Logic
// =============================================================================

// Skips are accumulated when a time out happened during a pkt transmission.
//
// A skip is transmitted at every start of link negotiation

assign skip_req = ((~accumed_skips_is_0)
  // This prevents a insertion tandard format SKP-OS between enhanced SKP-OS and SDS-OS
                   && ~((latched_sds_req || latched_ctlskp_req[0]) && (ltssm_cmd == `EPX16_SEND_IDLE) && (current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE))
                   && ((cxl_mode_enable && (xbyte_curnt_state != S_SKIP)) ? xmtbyte_txdata_dv : 1'b1)
                   );


// for Gen1/2 rate, if app_sris_mode==1, use short skip interval (153 Symbol Times)
wire [10:0] cfg_skip_interval_not_8gt = app_sris_mode ? ((`EPX16_CX_GEN2_MODE == 1 && `EPX16_CX_PL_FREQ_MULTIPLIER == 2 && current_data_rate == `EPX16_GEN2_RATE) ? `EPX16_DEFAULT_SHORT_SKIP_INTERVAL - 1 : `EPX16_DEFAULT_SHORT_SKIP_INTERVAL) : cfg_skip_interval;

// DEFAULT_GEN3_CXL_SNHB_INTERVAL = 340 for SyncHeader bypass
// DEFAULT_GEN3_CXL_SYNC_INTERVAL = 374 for SyncHeader not bypass

// for >=Gen3 rate, if app_sris_mode && cxl (not syncheader bypass), use DEFAULT_GEN3_SHORT_SKIP_INTERVAL_CXL = 34
// for Gen3 rate, if app_sris_mode==1, use short skip interval (37 Blocks)
wire sris_cxl = app_sris_mode && cxl_mode_enable;
wire [8:0] cfg_skip_interval_8gt =                                     app_sris_mode                 ? `EPX16_DEFAULT_GEN3_SHORT_SKIP_INTERVAL :
                                     `EPX16_DEFAULT_GEN3_SKIP_INTERVAL;

assign skip_insert =
                     ((current_data_rate == `EPX16_GEN5_RATE) && smlh_ltssm_state ==  `EPX16_S_LPBK_ACTIVE && active_nb == 8 && state_halt) ? 1'b0 :
                     (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? (skip_timer_130b >= {{(TIMER_BITS-9){1'b0}},cfg_skip_interval_8gt})
                                                                                          : (skip_timer >= cfg_skip_interval_not_8gt);

// do not send skip during compliance state and L0s, L1 and L2 state
always @(posedge core_clk or negedge core_rst_n)
begin : skip_timer_10b_proc
    if(!core_rst_n ) begin
        skip_timer      <= #TP {TIMER_BITS{1'b0}};
    end else if (skip_insert) begin
        skip_timer      <= #TP {TIMER_BITS{1'b0}};
    end else if (  (ltssm_cmd == `EPX16_SEND_BEACON)
                || (ltssm_cmd == `EPX16_XMT_IN_EIDLE)
                || ((ltssm_cmd == `EPX16_COMPLIANCE_PATTERN) && !cfg_compliance_sos)
                || ((ltssm_cmd == `EPX16_MOD_COMPL_PATTERN)  && !cfg_compliance_sos)
                || (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) // Disable counter in GEN3/4.
                ) begin
        skip_timer      <= #TP {TIMER_BITS{1'b0}};
    end else begin
        skip_timer     <= #TP  skip_timer + 1'b1;
    end
end

// do not send skip during compliance state and L0s, L1 and L2 state
always @(posedge core_clk or negedge core_rst_n)
begin : skip_timer_130b_proc
    if(!core_rst_n ) begin
        skip_timer_130b  <= #TP {TIMER_BITS{1'b0}};
    end else if ( skip_insert ) begin
        skip_timer_130b  <= #TP {TIMER_BITS{1'b0}};
    end else if (  (ltssm_cmd == `EPX16_SEND_BEACON)
                || (ltssm_cmd == `EPX16_XMT_IN_EIDLE)
                || ((xbyte_curnt_state == S_SDS || xbyte_curnt_state == S_SKIP) && cxl_mode_enable)
                || (ltssm_cmd == `EPX16_COMPLIANCE_PATTERN)
                || (ltssm_cmd == `EPX16_MOD_COMPL_PATTERN)
                || (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE) // Disable counter in GEN1/2.
                // Send the enhanced SKPOS when the LTSSM enters the Configuration.Idle or Recovery.Idle state
                || ((next_ctlskip_pat && latched_ctlskp_req[0] && load_pat))
                ) begin
        skip_timer_130b <= #TP {TIMER_BITS{1'b0}};
    end else if (xmtbyte_txstartblock[0]) begin // Increment at beginning of every block
        skip_timer_130b <= #TP  skip_timer_130b + 1'b1;
    end
end

        // Since we have plus1 priority higher than minus 1, we decided to
        // send one more skip under boundary condition so that we can have
        // this simplified logic.
assign prev_accumed_skips  = (   (ltssm_cmd == `EPX16_SEND_BEACON)
                               || (ltssm_cmd == `EPX16_XMT_IN_EIDLE)
                               || ((ltssm_cmd == `EPX16_COMPLIANCE_PATTERN) && !cfg_compliance_sos)
                               || ((ltssm_cmd == `EPX16_MOD_COMPL_PATTERN) && !cfg_compliance_sos)) ? 0
                                   : ( (skip_insert && cfg_compliance_sos && ((ltssm_cmd == `EPX16_COMPLIANCE_PATTERN) || (ltssm_cmd == `EPX16_MOD_COMPL_PATTERN)))
                                   || null_ieds_insert
                                   // During Loopback Mode at 8 GT/s, insert two SKP Ordered Sets
                                   ||  (skip_insert && ltssm_in_lpbk && (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE))
                                     )
                                   ? accumed_skips + 2'h2
                                   : ( (skip_insert)
                                     )
                                   ? accumed_skips + 1'b1 // There is a timing that a skip_insert and a xmtbyte_skip_sent are asserted at the same time when entrying a Data Stream at gen1 rate.
                                   : ((xmtbyte_skip_sent && accumed_skips_is_not_0)
                                     )
                                   ? accumed_skips - 1'b1 : accumed_skips;

always @(posedge core_clk or negedge core_rst_n)
begin : accumed_skips_proc
    if(!core_rst_n ) begin
        accumed_skips   <= #TP 0;
    end else begin
       accumed_skips   <= #TP prev_accumed_skips;
    end
end

assign accumed_skips_is_0 = (accumed_skips == 0);
assign accumed_skips_is_not_0 = (accumed_skips != 0);

assign null_ieds_for_skip_sent = 0;
assign xmtbyte_skip_sent_o = xmtbyte_skip_sent;

always @(posedge core_clk or negedge core_rst_n) begin : first_g5_eieos_sent_PROC
    if ( ~core_rst_n )
        first_g5_eieos_sent <= #TP 1'b0;
    else if ( current_data_rate < `EPX16_GEN5_RATE)
        first_g5_eieos_sent <= #TP 1'b0;
    else if ( ltssm_ts_halt )
        first_g5_eieos_sent <= #TP first_g5_eieos_sent;
    else if ( first_g5_eieos_sent && xmtbyte_eies_sent )
        first_g5_eieos_sent <= #TP 1'b0;
    else if ( xmtbyte_eies_sent )
        first_g5_eieos_sent <= #TP 1'b1;
end // first_g5_eieos_sent_PROC

// send two consecutive EIEOSs for gen5 rate
assign xmtbyte_g5_eies_sent = (current_data_rate >= `EPX16_GEN5_RATE) ? (first_g5_eieos_sent & xmtbyte_eies_sent) : xmtbyte_eies_sent;

// =============================================================================
// EIEOS Insertion Logic
// =============================================================================
always @(posedge core_clk or negedge core_rst_n)
begin : first_eieos_sent_proc
    if (!core_rst_n)
        first_eieos_sent <= #TP 1'b0;
    else if (!ltssm_ts_cnt_en
             && !n_fts_eieos_cnt_en
            ) //need hold ltssm_ts_cnt_en low until an eieos sent
        first_eieos_sent <= #TP 1'b0;
    else if (xmtbyte_g5_eies_sent)
        first_eieos_sent <= #TP 1'b1;
end

// Enable counter when sending FTS at Gen3/4 Rate
assign n_fts_eieos_cnt_en = ((current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) && (ltssm_cmd == `EPX16_SEND_N_FTS));

// Conditions for enabling counter for periodic EIEOS Insertion
// 1. LTSSM instruction to count consecutive TS1/TS2 Ordered Sets
// 2. Sending FTS Ordered Sets at Gen3 Rate
assign eieos_cnt_en = (ltssm_ts_cnt_en
                       || n_fts_eieos_cnt_en
                      )
                      & first_eieos_sent;

// if mask_eieos on any lane, send an eieos when eieos_req_cnt == 65535.
// if !mask_eieos, send an eieos when eieos_req_cnt == 31.
// should consider corner case where mask_eieos falling edge and eieos_req_cnt == 31
assign eieos_required = (( ((eieos_req_cnt_is_31 )
                           && !(int_any_mask_eieos | any_mask_eieos_fall_edge | latched_ctlskp_req[1])
                          )
                         || (eieos_req_cnt_is_ts1_num && int_any_mask_eieos)
                        ) && 
                             (ltssm_ts_cnt_en
                              || n_fts_eieos_cnt_en
                             ) & (ltssm_cmd != `EPX16_XMT_IN_EIDLE) // no Tx eieos during TxElecIdle
                       ) | ltssm_eq_rlock; // ltssm_eq_rlock means sending eieos from very begining of R.Lock from R.Eq to avoid 33 TS send

assign eieos_req_cnt_is_31      = (eieos_req_cnt == 31);
assign eieos_req_cnt_is_ts1_num = (eieos_req_cnt == TS1_NUM);

always @(posedge core_clk or negedge core_rst_n)
begin : eieos_cnt_proc
    if (!core_rst_n) begin
        eieos_req_cnt <= #TP 31;
    end else if ( (current_data_rate >= `EPX16_GEN3_RATE && ltssm_in_lpbk && !(lpbk_master && eieos_req_cnt >= 31 && !latched_eieos_sent))) begin
        eieos_req_cnt <= #TP 0;
    end else if (ltssm_cmd == `EPX16_XMT_IN_EIDLE) begin //reset eieos count to 31 so that the core txes eieos immediately after TxElecIdle if necessary (ltssm_ts_cnt_en = 1)
        eieos_req_cnt <= #TP 31;
    // Hold counter at reset value when not enabled
    end else if (!eieos_cnt_en || (ltssm_cmd_send_eieos_for_pset_map)) begin
      begin
        eieos_req_cnt <= #TP 31;
      end
    end else if (ltssm_ts_cnt_rst
             || ((any_mask_eieos_fall_edge || |eqctl_2mask_eieos_pulse) && EIEOS_MASK_LOGIC_ENABLE==0) //2xTS1 rcvd on any lane with reset EIEOS bit
            ) begin
        eieos_req_cnt <= #TP 0;
    end else if (eieos_cnt_en & (|xmtbyte_ts1_sent
                          || xmlh_pat_fts_sent
                          || |xmtbyte_ts2_sent)) begin
        eieos_req_cnt <= #TP
                             int_any_mask_eieos ? ( (eieos_req_cnt >= TS1_NUM) ? 0 : (eieos_req_cnt + 1'b1) ) :
                             ( (eieos_req_cnt >= 31) ? 0 : (eieos_req_cnt + 1'b1) );

    end
end

// =============================================================================

// number of cycles to pass one CX_DW of data
wire [31:0] data_cycles_i; // to avoid spyglass warning
assign data_cycles_i = (NBYTE >> active_nb_shift) >> OneHotToBitNum(int_link_mode[4:0]);
always @(posedge core_clk or negedge core_rst_n)
    if(!core_rst_n )
        data_cycles <= #TP 0;
    else
        data_cycles <= #TP data_cycles_i[SLICE_WD:0];

// to avoid spyglass warning
wire    [SLICE_WD:0]  data_cycles_sub_1_i;
wire    [SLICE_WD-1:0] data_cycles_sub_1;
assign data_cycles_sub_1_i = (data_cycles - 1'b1);
assign data_cycles_sub_1 = data_cycles_sub_1_i[SLICE_WD-1:0];
// data_cycles is essentially static
// next_xdlh_xmlh_eot is the look-ahead version from the xdlh_control
reg [SLICE_WD-1:0] pkt_end_cnt;
always @(posedge core_clk or negedge core_rst_n)
begin : pkt_end_cnt_proc
    if(!core_rst_n ) begin
        pkt_end_cnt <= #TP 5'b0;
    end else begin
        pkt_end_cnt <= #TP Calc_pkt_end(next_xdlh_xmlh_eot, data_cycles);
    end
end




reg      early_pkt_end;
always @(posedge core_clk or negedge core_rst_n)
begin : early_pkt_end_proc
    if(!core_rst_n ) begin
        early_pkt_end <= #TP 1'b0;
    end else begin
        early_pkt_end <= #TP nw_gtr_2 & (data_cycles != 1) && next_eot_is_last && |(next_xdlh_xmlh_eot) && !next_xdlh_xmlh_eot[NW-1] ||
                       !nw_gtr_2 & (data_cycles != 1) && |(next_xdlh_xmlh_eot) && !next_xdlh_xmlh_eot[NW-1];
    end
end

assign  last_chunk  = (txdata_dv0_required) ? 1'b0                               :
                      (early_pkt_end)       ? (chunk_cnt == pkt_end_cnt)         :
                                              (chunk_cnt == data_cycles_sub_1) ; //(data_cycles - 1'b1)


// chunk counter
always @(posedge core_clk or negedge core_rst_n)
begin : chunk_cnt_proc
    if (!core_rst_n)
        chunk_cnt   <= #TP 0;
    else
        if ( last_chunk || !pkt_in_progress_i
             || (!cmd_is_data && (xbyte_curnt_state != S_CMP))) // reset the chunk count went we get new data
            chunk_cnt   <= #TP 0;
        else if (txdata_dv0_required) // Do not increment chunk counter during TxDataSKip/!TxDataValid
            chunk_cnt   <= #TP chunk_cnt;
        else
            chunk_cnt   <= #TP chunk_cnt + 1'b1;
end

assign active_nb_shift = int_active_nb[2:1];                             // 1s = 0 , 2s = 1, 4s = 2
assign active_nb_shift_os = active_nb_shift;

reg [2:0] chunk_cnt_shift ; // Used to multiply increment of chunk_cnt

always @(*)
begin
  case({int_active_nb[4:0],active_lane_cnt})
      10'b00010_00001 : chunk_cnt_shift = 1;  // 2sx1
      10'b00010_00010 : chunk_cnt_shift = 2;  // 2sx2
      10'b00010_00100 : chunk_cnt_shift = 3;  // 2sx4
      10'b00010_01000 : chunk_cnt_shift = 4;  // 2sx8
      10'b00010_10000 : chunk_cnt_shift = 5;  // 2sx16 in 512b
      10'b00100_00001 : chunk_cnt_shift = 2;  // 4sx1
      10'b00100_00010 : chunk_cnt_shift = 3;  // 4sx2
      10'b00100_00100 : chunk_cnt_shift = 4;  // 4sx4
      10'b00100_01000 : chunk_cnt_shift = 5;  // 4sx8
      10'b00100_10000 : chunk_cnt_shift = 6;  // 4sx16
      default      : chunk_cnt_shift = 0;  // Full Datapath in operation
  endcase
end

assign chunk_offset = chunk_cnt << chunk_cnt_shift;

// keep the xdlh_data halted until all of it has been processed
assign  xmtbyte_xdlh_halt   =                                                                     (cmd_is_data && !txdata_dv0_required) ? !(last_chunk && pkt_in_progress) : 1'b1;

assign  xmtbyte_xdlh_halt_o = /* */ xmtbyte_xdlh_halt;

always @( posedge core_clk or negedge core_rst_n ) begin : xmtbyte_xdlh_halt_d_PROC
    if ( ~core_rst_n )
        xmtbyte_xdlh_halt_d <= #TP 0;
    else
        xmtbyte_xdlh_halt_d <= #TP xmtbyte_xdlh_halt;
end // xmtbyte_xdlh_halt_d_PROC


// Aligen to xbyte_next_state
assign  pkt_in_progress     = ( cmd_is_data && (or_stp || or_sdp) && !(skip_insert || skip_req)) || latched_pkt_in_progress;
assign  pkt_in_progress_i   = pkt_in_progress;


// valid_pkt_in_progress is always asserted for TLPs. For DLLPs, if it is a PM
// DLLP then the SDP token is checked against the current_data_rate. If there
// is an error, IDLs are transmitted instead of the DLLP. pkt_in_progress is
// used for advancing state machine and unhalting datapath.
// valid_pkt_in_progress is used for selecting valid xmt data
assign  valid_pkt_in_progress = ( cmd_is_data && (or_stp || valid_sdp) && !(skip_insert || skip_req)) || valid_latched_pkt_in_progress;
assign  valid_pkt_in_progress_i = valid_pkt_in_progress;

always @(posedge core_clk or negedge core_rst_n) begin : latched_pkt_in_progress_PROC
    if (!core_rst_n) begin
        latched_pkt_in_progress         <= #TP 1'b0;
        valid_latched_pkt_in_progress   <= #TP 1'b0;
    end else begin
//pcie_perf
        if ( current_data_rate == `EPX16_GEN5_RATE && smlh_ltssm_state == `EPX16_S_LPBK_ACTIVE && active_nb == 8 && state_halt && (skip_timer_130b >= {{(TIMER_BITS-9){1'b0}},cfg_skip_interval_8gt}) ) begin
            latched_pkt_in_progress       <= #TP latched_pkt_in_progress;
            valid_latched_pkt_in_progress <= #TP valid_latched_pkt_in_progress;
        end else
        if ((eot_is_last && !xmtbyte_xdlh_halt) ) begin
            latched_pkt_in_progress         <= #TP 1'b0;
            valid_latched_pkt_in_progress   <= #TP 1'b0;
        end
        else begin
            latched_pkt_in_progress         <= #TP pkt_in_progress;
            valid_latched_pkt_in_progress   <= #TP valid_pkt_in_progress;
        end
    end
end

// =============================================================================
// 128b/130b Block Encoding Logic.
// Generation of xmtbyte_txstartblock and xmtbyte_txdata_dv
// =============================================================================

// 128b/130b Transmit Counter Enable
// Disable counter when in Electrical Idle or non-Gen3 data rate
// Enable symbol counter when loading first OS at Gen3 data rate
always @(posedge core_clk or negedge core_rst_n)
begin : xmt_cnt_130b_en_proc
    if(!core_rst_n) begin
        xmt_cnt_130b_en <= #TP 1'b0;
    end
    else begin
        if((ltssm_cmd == `EPX16_XMT_IN_EIDLE) || (current_data_rate == `EPX16_GEN1_RATE || current_data_rate == `EPX16_GEN2_RATE))
            xmt_cnt_130b_en <= #TP 1'b0;
        else if (load_pat)
            xmt_cnt_130b_en <= #TP 1'b1;
    end
end

// 128b/130b Transmit Counter.
// When running at 8 GT/s, this is the master counter from which txstartblock
// and txdata_dv are derived. This counter is also used to derive the
// transmitted symbol counter.
// At 8 GT/s, data tranmission follows a periodic sequence. For 64 consecutive
// clock cycles, valid data is tranmitted. After 64 cycles of valid data,
// txdata_dv is de-asserted for a single clock cycle.
// txstartblock is asserted every 4*active_nb clock cycles during the first
// symbol of each block.
// The counter incrementes every clock cycle and rolls over at 7'd65 (0x40)
always @(posedge core_clk or negedge core_rst_n)
begin : xmt_cnt_130b_proc
    if (!core_rst_n) begin
        xmt_cnt_130b <= #TP 7'h00;
    end else begin
        if ((!xmt_cnt_130b_en) || (xmt_cnt_130b == 7'h40) || ((xmt_cnt_130b == 7'h3f) && (ltssm_cxl_sh_bypass))) begin
            xmt_cnt_130b <= #TP 7'h00;
        end else begin
            xmt_cnt_130b <= #TP xmt_cnt_130b + ( 7'h01); // 40 bytes for gen6 skp
        end
    end
end
assign xmt_cnt_130b_is_63 = (xmt_cnt_130b == 63);
assign xmt_cnt_130b_is_64 = (xmt_cnt_130b == 64);

// Transmitted Symbol Counter. Counts transmitted symbols
// allowing blocks to be counted and tracked.
// Counter is shifted to account for the number of symbols
// transmitted per lane per clock cycle
wire [6:0] xmt_sym_cnt_i; // to avoid spyglass warning
assign xmt_sym_cnt_i = (xmt_cnt_130b << active_nb_shift_os);
assign xmt_sym_cnt = xmt_sym_cnt_i[3:0];

// txstartblock_mask is used to control the period of
// xmtbyte_startblock assertion.

always @(*)
begin : txstartblock_mask_proc
    case (int_active_nb[4:0])
        5'b00010  : txstartblock_mask = 7'b1000111; // 2s: TxStartBlock every 8 clk cycles
        5'b00100  : txstartblock_mask = 7'b1000011; // 4s: TxStartBlock every 4 clk cycles
        default  : txstartblock_mask = 7'b0000000;
    endcase
end

// xmtbyte_txstartblock is asserted during the first symbol of a 130-bit block.
// This occurs whenever the bitwise AND of the transmit counter and the
// TxStartBlock mask equals 0.
// The mask is modified according to the number of symbols transmitted
// per clock cycle.
// 1s : xmtbyte_txstartblock asserted every 16 clock cycles
// 2s : xmtbyte_txstartblock asserted every  8 clock cycles
// 4s : xmtbyte_txstartblock asserted every  4 clock cycles
assign xmtbyte_txstartblock_i[0] = (xmt_cnt_130b_en && ((xmt_cnt_130b & txstartblock_mask) == 7'h00));

always @(posedge core_clk or negedge core_rst_n)
begin : xmtbyte_txstartblock_proc
    if(!core_rst_n) begin
        int_xmtbyte_txstartblock <= #TP {NL*TXSB_WD{1'b0}};
    end
    else if(int_eidle_i)
        int_xmtbyte_txstartblock <= #TP {NL*TXSB_WD{1'b0}};
    else begin
        if ( xmtbyte_txstartblock_i[0] )
            int_xmtbyte_txstartblock <= #TP {NL*TXSB_WD{1'b1}};
        else
            int_xmtbyte_txstartblock <= #TP {NL*TXSB_WD{1'b0}};
    end
end

assign xmtbyte_txstartblock = int_xmtbyte_txstartblock;

// txdata_dv0_required indicates that txdata_dv is about to be de-asserted.
// This happens periodically every 64 clock cycles. This signal is used to
// halt the data path for a single cycle when txdata_dv is de-asserted
assign txdata_dv0_required = (ltssm_cxl_sh_bypass) ? 1'b0 : (xmt_cnt_130b == 7'h40);

// De-assert txdata_dv every 64 clock cycles. This occurs every time the
// transmit counter rolls over to 0.
assign int_txdata_dv = ( xmt_cnt_130b != 7'h00) & !int_eidle;

assign datablock_syncheader = {{(NB-2){1'b0}},`EPX16_SYNC_DATA_BLOCK};
assign osblock_syncheader   = {{(NB-2){1'b0}},`EPX16_SYNC_OS_BLOCK};


wire [NL-1:0] lpbk_entry_not_lane_under_test_for_master;
wire lpbk_entry_for_lpbk_master = ((current_data_rate < `EPX16_GEN3_RATE) && (ltssm_cmd == `EPX16_SEND_TS1) && lpbk_master && ltssm_in_lpbkentry);
wire lpbk_entry_for_eq_from_lpbk_master = lpbk_entry_for_lpbk_master & |lpbk_eq_lanes_active;
assign lpbk_entry_not_lane_under_test_for_master = {NL{lpbk_entry_for_eq_from_lpbk_master}} & ~lpbk_eq_lanes_active;

assign xmtbyte_txdata_dv =
                         (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? int_txdata_dv :
                         1'b1;

// =============================================================================
// Receive Error Counters for compliance
// =============================================================================

wire    [NL-1:0]        err_inc;
wire    [NL-1:0]        dlyd_err_inc;

assign  err_inc  = rpipe_rxerror;

assign  dlyd_err_inc  = {NL{1'b0}};

// latch pattern lock
always @(posedge core_clk or negedge core_rst_n)
if (!core_rst_n)
    pattern_lock                <= #TP {NL{1'b0}};
else
    if ((xbyte_curnt_state != S_CMP) && !((ltssm_cmd == `EPX16_MOD_COMPL_PATTERN) && (xbyte_curnt_state == S_SKIP)))
        pattern_lock            <= #TP {NL{1'b0}};
    else begin
        pattern_lock            <= #TP smlh_mcs_rcvd | pattern_lock;
    end



always @(posedge core_clk or negedge core_rst_n)
if (!core_rst_n)
    err_cnt <= #TP {NL{7'b0}};
else begin
    for (int i = 0; i < NL; i = i + 1) begin
        err_cnt[i*7 +: 7] <= #TP pattern_lock[i] ? ( ((err_cnt[i*7 +: 7] + err_inc[i] + dlyd_err_inc[i]) >= 127) ? 7'd127 : 7'(err_cnt[i*7 +: 7] + err_inc[i] + dlyd_err_inc[i]) ) : err_cnt[i*7 +: 7];
    end
end

// in the first clock of Recovery.Speed state load_pat might be low for TxDataValid but the pending TS2 needs to be sent.
// So load the ltssm_ts_auto_change in the second clock. ltssm_ts_auto_change_r (delay one cycle to ltssm_ts_auto_change) is for this.
always @(posedge core_clk or negedge core_rst_n) begin : ltssm_ts_auto_change_r_PROC
    if ( ~core_rst_n )
        ltssm_ts_auto_change_r <= #TP 0;
    else
        ltssm_ts_auto_change_r <= #TP ltssm_ts_auto_change;
end // ltssm_ts_auto_change_r_PROC

// Latch these signals because they can change while being used
//delay ltssm_ts_auto_change by 1 cycle because this signal updates at the same cycle when ltssm enters new state.
//If load_pat asserts at the same cycle when ltssm enters new state, the core still sends previous TS but with new state ltssm_ts_auto_change.
always @( * ) begin : ltssm_ts_auto_change_newltssm_d_PROC
    ltssm_ts_auto_change_d = (smlh_ltssm_state == `EPX16_S_RCVRY_SPEED) ? ltssm_ts_auto_change_r : ltssm_ts_auto_change;
end // ltssm_ts_auto_change_newltssm_d_PROC

// Alternate Protocols informatiom for sym14-8
always @(posedge core_clk or negedge core_rst_n)
if (!core_rst_n) begin
    latched_ts_cntrl            <= #TP 0;
    latched_mod_ts              <= #TP 0;
    latched_ts_alt_protocol     <= #TP 0;
    latched_ts_auto_change_i    <= #TP 0;
    latched_ts_data_rate_i      <= #TP 0;
    int_xlinknum                <= #TP 8'h00;
    int_xk237_4lnknum           <= #TP {NL{1'b1}};
    int_xk237_4lannum           <= #TP {NL{1'b1}};
    latched_alt_prot_info       <= #TP 0;
end else if (load_pat_easy) begin
    latched_ts_cntrl            <= #TP ltssm_ts_cntrl;
    latched_mod_ts              <= #TP ltssm_mod_ts;
    latched_ts_alt_protocol     <= #TP ltssm_ts_alt_protocol;
    latched_ts_auto_change_i    <= #TP ltssm_ts_auto_change_d;
    latched_alt_prot_info       <= #TP ltssm_ts_alt_prot_info;
    latched_ts_data_rate_i      <= #TP ltssm_ts_data_rate;
    int_xlinknum                <= #TP ltssm_xlinknum;
    int_xk237_4lnknum           <= #TP ltssm_xk237_4lnknum;
    int_xk237_4lannum           <= #TP ltssm_xk237_4lannum;
end


// =============================================================================
// Data Path
// =============================================================================
// At 2.5 GT/s and 5 GT/s, PAD Symbols are inserted to preserve link alignment
// when a TLP does not finish on the last lane of the link.
// At 8 GT/s, IDL Tokens are inserted to preserve link alignement when a
// TLP does not finish on the last lane of the link.
assign  int_pad_data        = (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? {`EPX16_LIDLE_TOKEN, `EPX16_LIDLE_TOKEN, `EPX16_LIDLE_TOKEN, `EPX16_LIDLE_TOKEN } :
                                                                                                     {`EPX16_PAD_8B,      `EPX16_PAD_8B,      `EPX16_PAD_8B,      `EPX16_PAD_8B      } ;

always @(*) begin : PAD_DATA
    integer i;
    for(i = 0; i < NW; i = i + 1) begin
        pad_data[i*32 +: 32] = int_xdlh_pad[i] ? int_pad_data : xdlh_xmlh_data[i*32 +: 32];
    end
    // int_xdlh_pad[0] can never be set
    pad_data[31:0] = xdlh_xmlh_data[31:0];
end

// Per lane compliance data
//      cmp_data[(01*NBIT)-1:(00*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(01*7)-1:00*7], pattern_lock[00], int_active_nb, cmp_dly_lane[0], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(1 *NBIT)-1:(0 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(1 *7)-1:0 *7], pattern_lock[0 ], int_active_nb, cmp_dly_lane[0], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(2 *NBIT)-1:(1 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(2 *7)-1:1 *7], pattern_lock[1 ], int_active_nb, cmp_dly_lane[1], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(3 *NBIT)-1:(2 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(3 *7)-1:2 *7], pattern_lock[2 ], int_active_nb, cmp_dly_lane[2], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(4 *NBIT)-1:(3 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(4 *7)-1:3 *7], pattern_lock[3 ], int_active_nb, cmp_dly_lane[3], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(5 *NBIT)-1:(4 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(5 *7)-1:4 *7], pattern_lock[4 ], int_active_nb, cmp_dly_lane[4], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(6 *NBIT)-1:(5 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(6 *7)-1:5 *7], pattern_lock[5 ], int_active_nb, cmp_dly_lane[5], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(7 *NBIT)-1:(6 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(7 *7)-1:6 *7], pattern_lock[6 ], int_active_nb, cmp_dly_lane[6], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(8 *NBIT)-1:(7 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(8 *7)-1:7 *7], pattern_lock[7 ], int_active_nb, cmp_dly_lane[7], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(9 *NBIT)-1:(8 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(9 *7)-1:8 *7], pattern_lock[8 ], int_active_nb, cmp_dly_lane[0], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(10*NBIT)-1:(9 *NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(10*7)-1:9 *7], pattern_lock[9 ], int_active_nb, cmp_dly_lane[1], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(11*NBIT)-1:(10*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(11*7)-1:10*7], pattern_lock[10], int_active_nb, cmp_dly_lane[2], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(12*NBIT)-1:(11*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(12*7)-1:11*7], pattern_lock[11], int_active_nb, cmp_dly_lane[3], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(13*NBIT)-1:(12*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(13*7)-1:12*7], pattern_lock[12], int_active_nb, cmp_dly_lane[4], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(14*NBIT)-1:(13*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(14*7)-1:13*7], pattern_lock[13], int_active_nb, cmp_dly_lane[5], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(15*NBIT)-1:(14*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(15*7)-1:14*7], pattern_lock[14], int_active_nb, cmp_dly_lane[6], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);
assign  cmp_data[(16*NBIT)-1:(15*NBIT)] = BuildCmpData( xmlh_pat_data, xmlh_dly_cmp_data, err_cnt[(16*7)-1:15*7], pattern_lock[15], int_active_nb, cmp_dly_lane[7], xmlh_cmp_errloc, xmlh_dly_cmp_errloc);

// Per lane Compliance k char
//      cmp_datak[(01*NBK)-1:(00*NBK)]  = cmp_dly_lane[0] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(1 *NBK)-1:(0 *NBK)]  = cmp_dly_lane[0] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(2 *NBK)-1:(1 *NBK)]  = cmp_dly_lane[1] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(3 *NBK)-1:(2 *NBK)]  = cmp_dly_lane[2] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(4 *NBK)-1:(3 *NBK)]  = cmp_dly_lane[3] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(5 *NBK)-1:(4 *NBK)]  = cmp_dly_lane[4] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(6 *NBK)-1:(5 *NBK)]  = cmp_dly_lane[5] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(7 *NBK)-1:(6 *NBK)]  = cmp_dly_lane[6] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(8 *NBK)-1:(7 *NBK)]  = cmp_dly_lane[7] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(9 *NBK)-1:(8 *NBK)]  = cmp_dly_lane[0] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(10*NBK)-1:(9 *NBK)]  = cmp_dly_lane[1] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(11*NBK)-1:(10*NBK)]  = cmp_dly_lane[2] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(12*NBK)-1:(11*NBK)]  = cmp_dly_lane[3] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(13*NBK)-1:(12*NBK)]  = cmp_dly_lane[4] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(14*NBK)-1:(13*NBK)]  = cmp_dly_lane[5] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(15*NBK)-1:(14*NBK)]  = cmp_dly_lane[6] ? xmlh_dly_cmp_datak : xmlh_pat_datak;
assign  cmp_datak[(16*NBK)-1:(15*NBK)]  = cmp_dly_lane[7] ? xmlh_dly_cmp_datak : xmlh_pat_datak;


// combined xdlh & compliance data
assign  int_xdlh_data   = pad_data;
assign  int_xdlh_datak  = xdlh_xmlh_datak;

always @( posedge core_clk or negedge core_rst_n ) begin : xmlh_pat_dv_d_PROC
    if ( ~core_rst_n )
        xmlh_pat_dv_d <= #TP 0;
    else
        xmlh_pat_dv_d <= #TP xmlh_pat_dv;
end // xmlh_pat_dv_d_PROC

// Per lane data
assign  int_xmt_data[(0 *NBIT)+(NBIT)-1:(0 *NBIT)]  = GetSlice( int_xdlh_data, (0  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[0]  );
assign  int_xmt_data[(1 *NBIT)+(NBIT)-1:(1 *NBIT)]  = GetSlice( int_xdlh_data, (1  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[1]  );
assign  int_xmt_data[(2 *NBIT)+(NBIT)-1:(2 *NBIT)]  = GetSlice( int_xdlh_data, (2  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[2]  );
assign  int_xmt_data[(3 *NBIT)+(NBIT)-1:(3 *NBIT)]  = GetSlice( int_xdlh_data, (3  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[3]  );
assign  int_xmt_data[(4 *NBIT)+(NBIT)-1:(4 *NBIT)]  = GetSlice( int_xdlh_data, (4  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[4]  );
assign  int_xmt_data[(5 *NBIT)+(NBIT)-1:(5 *NBIT)]  = GetSlice( int_xdlh_data, (5  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[5]  );
assign  int_xmt_data[(6 *NBIT)+(NBIT)-1:(6 *NBIT)]  = GetSlice( int_xdlh_data, (6  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[6]  );
assign  int_xmt_data[(7 *NBIT)+(NBIT)-1:(7 *NBIT)]  = GetSlice( int_xdlh_data, (7  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[7]  );
assign  int_xmt_data[(8 *NBIT)+(NBIT)-1:(8 *NBIT)]  = GetSlice( int_xdlh_data, (8  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[8]  );
assign  int_xmt_data[(9 *NBIT)+(NBIT)-1:(9 *NBIT)]  = GetSlice( int_xdlh_data, (9  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[9]  );
assign  int_xmt_data[(10*NBIT)+(NBIT)-1:(10*NBIT)]  = GetSlice( int_xdlh_data, (10 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[10] );
assign  int_xmt_data[(11*NBIT)+(NBIT)-1:(11*NBIT)]  = GetSlice( int_xdlh_data, (11 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[11] );
assign  int_xmt_data[(12*NBIT)+(NBIT)-1:(12*NBIT)]  = GetSlice( int_xdlh_data, (12 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[12] );
assign  int_xmt_data[(13*NBIT)+(NBIT)-1:(13*NBIT)]  = GetSlice( int_xdlh_data, (13 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[13] );
assign  int_xmt_data[(14*NBIT)+(NBIT)-1:(14*NBIT)]  = GetSlice( int_xdlh_data, (14 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[14] );
assign  int_xmt_data[(15*NBIT)+(NBIT)-1:(15*NBIT)]  = GetSlice( int_xdlh_data, (15 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[15] );

// Per lane k char
assign  int_xmt_datak[(0 *NBK)+(NBK)-1:(0 *NBK)]    = GetKSlice( int_xdlh_datak, (0  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[0]  );
assign  int_xmt_datak[(1 *NBK)+(NBK)-1:(1 *NBK)]    = GetKSlice( int_xdlh_datak, (1  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[1]  );
assign  int_xmt_datak[(2 *NBK)+(NBK)-1:(2 *NBK)]    = GetKSlice( int_xdlh_datak, (2  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[2]  );
assign  int_xmt_datak[(3 *NBK)+(NBK)-1:(3 *NBK)]    = GetKSlice( int_xdlh_datak, (3  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[3]  );
assign  int_xmt_datak[(4 *NBK)+(NBK)-1:(4 *NBK)]    = GetKSlice( int_xdlh_datak, (4  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[4]  );
assign  int_xmt_datak[(5 *NBK)+(NBK)-1:(5 *NBK)]    = GetKSlice( int_xdlh_datak, (5  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[5]  );
assign  int_xmt_datak[(6 *NBK)+(NBK)-1:(6 *NBK)]    = GetKSlice( int_xdlh_datak, (6  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[6]  );
assign  int_xmt_datak[(7 *NBK)+(NBK)-1:(7 *NBK)]    = GetKSlice( int_xdlh_datak, (7  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[7]  );
assign  int_xmt_datak[(8 *NBK)+(NBK)-1:(8 *NBK)]    = GetKSlice( int_xdlh_datak, (8  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[8]  );
assign  int_xmt_datak[(9 *NBK)+(NBK)-1:(9 *NBK)]    = GetKSlice( int_xdlh_datak, (9  + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[9]  );
assign  int_xmt_datak[(10*NBK)+(NBK)-1:(10*NBK)]    = GetKSlice( int_xdlh_datak, (10 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[10] );
assign  int_xmt_datak[(11*NBK)+(NBK)-1:(11*NBK)]    = GetKSlice( int_xdlh_datak, (11 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[11] );
assign  int_xmt_datak[(12*NBK)+(NBK)-1:(12*NBK)]    = GetKSlice( int_xdlh_datak, (12 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[12] );
assign  int_xmt_datak[(13*NBK)+(NBK)-1:(13*NBK)]    = GetKSlice( int_xdlh_datak, (13 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[13] );
assign  int_xmt_datak[(14*NBK)+(NBK)-1:(14*NBK)]    = GetKSlice( int_xdlh_datak, (14 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[14] );
assign  int_xmt_datak[(15*NBK)+(NBK)-1:(15*NBK)]    = GetKSlice( int_xdlh_datak, (15 + chunk_offset), int_active_nb, active_lane_cnt, int_lanes_active_for_data[15] );


// only insert link and lane numbers if we aren't putting K237 there
assign  insert_linknum  = {NL{xmlh_pat_linkloc[0]}} & ~int_xk237_4lnknum;
assign  insert_lanenum  = {NL{xmlh_pat_laneloc[0]}} & ~int_xk237_4lannum;
assign insert_eqts_info = latched_mod_ts ? 4'h0 : xmlh_pat_eqloc;
assign int_eqts_info    = latched_eqts_info;
assign int_mask_eieos   = latched_mod_ts ? {NL{1'b0}} : EIEOS_MASK_LOGIC_ENABLE ? {NL{xmlh_pat_eieosloc}} & eqctl_mask_eieos & {NL{~rollover_eieos_req}} : {NL{1'b0}};


assign insert_s15_4     = latched_mod_ts ? xmlh_pat_s15_4loc : 0;
assign insert_s6        = latched_mod_ts ? xmlh_pat_eqloc[0] : 0;

wire g6_n_pch, g6_y_pch, g6_rate, g6_ts0, g6_prior_ts0;
assign g6_n_pch = 1'b0;
assign g6_y_pch = 1'b0;
assign g6_rate  = 1'b0;
assign g6_ts0   = 1'b0;
assign g6_prior_ts0 = 1'b0;

// Per lane pattern data
assign    i_pat_data[(0 *PAT_NBIT)+(PAT_NBIT)-1:(0 *PAT_NBIT)]  = BuildOsData( xmlh_pat_data[(0 *PAT_NBIT)+(PAT_NBIT)-1:(0 *PAT_NBIT)], int_xlinknum, 8'd0,  int_active_nb, insert_linknum[0],  insert_lanenum[0],  insert_eqts_info, int_eqts_info[EQTS_WD*1-1 : EQTS_WD*0],   int_mask_eieos[0]);
assign    i_pat_data[(1 *NBIT)+(NBIT)-1:(1 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd1,  int_active_nb, insert_linknum[1],  insert_lanenum[1],  insert_eqts_info, int_eqts_info[EQTS_WD*2-1 : EQTS_WD*1],   int_mask_eieos[1]);
assign    i_pat_data[(2 *NBIT)+(NBIT)-1:(2 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd2,  int_active_nb, insert_linknum[2],  insert_lanenum[2],  insert_eqts_info, int_eqts_info[EQTS_WD*3-1 : EQTS_WD*2],   int_mask_eieos[2]);
assign    i_pat_data[(3 *NBIT)+(NBIT)-1:(3 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd3,  int_active_nb, insert_linknum[3],  insert_lanenum[3],  insert_eqts_info, int_eqts_info[EQTS_WD*4-1 : EQTS_WD*3],   int_mask_eieos[3]);
assign    i_pat_data[(4 *NBIT)+(NBIT)-1:(4 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd4,  int_active_nb, insert_linknum[4],  insert_lanenum[4],  insert_eqts_info, int_eqts_info[EQTS_WD*5-1 : EQTS_WD*4],   int_mask_eieos[4]);
assign    i_pat_data[(5 *NBIT)+(NBIT)-1:(5 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd5,  int_active_nb, insert_linknum[5],  insert_lanenum[5],  insert_eqts_info, int_eqts_info[EQTS_WD*6-1 : EQTS_WD*5],   int_mask_eieos[5]);
assign    i_pat_data[(6 *NBIT)+(NBIT)-1:(6 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd6,  int_active_nb, insert_linknum[6],  insert_lanenum[6],  insert_eqts_info, int_eqts_info[EQTS_WD*7-1 : EQTS_WD*6],   int_mask_eieos[6]);
assign    i_pat_data[(7 *NBIT)+(NBIT)-1:(7 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd7,  int_active_nb, insert_linknum[7],  insert_lanenum[7],  insert_eqts_info, int_eqts_info[EQTS_WD*8-1 : EQTS_WD*7],   int_mask_eieos[7]);
assign    i_pat_data[(8 *NBIT)+(NBIT)-1:(8 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd8,  int_active_nb, insert_linknum[8],  insert_lanenum[8],  insert_eqts_info, int_eqts_info[EQTS_WD*9-1 : EQTS_WD*8],   int_mask_eieos[8]);
assign    i_pat_data[(9 *NBIT)+(NBIT)-1:(9 *NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd9,  int_active_nb, insert_linknum[9],  insert_lanenum[9],  insert_eqts_info, int_eqts_info[EQTS_WD*10-1 : EQTS_WD*9],  int_mask_eieos[9]);
assign    i_pat_data[(10*NBIT)+(NBIT)-1:(10*NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd10, int_active_nb, insert_linknum[10], insert_lanenum[10], insert_eqts_info, int_eqts_info[EQTS_WD*11-1 : EQTS_WD*10], int_mask_eieos[10]);
assign    i_pat_data[(11*NBIT)+(NBIT)-1:(11*NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd11, int_active_nb, insert_linknum[11], insert_lanenum[11], insert_eqts_info, int_eqts_info[EQTS_WD*12-1 : EQTS_WD*11], int_mask_eieos[11]);
assign    i_pat_data[(12*NBIT)+(NBIT)-1:(12*NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd12, int_active_nb, insert_linknum[12], insert_lanenum[12], insert_eqts_info, int_eqts_info[EQTS_WD*13-1 : EQTS_WD*12], int_mask_eieos[12]);
assign    i_pat_data[(13*NBIT)+(NBIT)-1:(13*NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd13, int_active_nb, insert_linknum[13], insert_lanenum[13], insert_eqts_info, int_eqts_info[EQTS_WD*14-1 : EQTS_WD*13], int_mask_eieos[13]);
assign    i_pat_data[(14*NBIT)+(NBIT)-1:(14*NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd14, int_active_nb, insert_linknum[14], insert_lanenum[14], insert_eqts_info, int_eqts_info[EQTS_WD*15-1 : EQTS_WD*14], int_mask_eieos[14]);
assign    i_pat_data[(15*NBIT)+(NBIT)-1:(15*NBIT)]  = BuildOsData( xmlh_pat_data, int_xlinknum, 8'd15, int_active_nb, insert_linknum[15], insert_lanenum[15], insert_eqts_info, int_eqts_info[EQTS_WD*16-1 : EQTS_WD*15], int_mask_eieos[15]);

assign  int_pat_data[(0 *NBIT)+(NBIT)-1:(0 *NBIT)]    = BuildTsData( i_pat_data[0 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[0 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[0]);
assign  int_pat_data[(1 *NBIT)+(NBIT)-1:(1 *NBIT)]    = BuildTsData( i_pat_data[1 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[1 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[1]);
assign  int_pat_data[(2 *NBIT)+(NBIT)-1:(2 *NBIT)]    = BuildTsData( i_pat_data[2 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[2 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[2]);
assign  int_pat_data[(3 *NBIT)+(NBIT)-1:(3 *NBIT)]    = BuildTsData( i_pat_data[3 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[3 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[3]);
assign  int_pat_data[(4 *NBIT)+(NBIT)-1:(4 *NBIT)]    = BuildTsData( i_pat_data[4 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[4 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[4]);
assign  int_pat_data[(5 *NBIT)+(NBIT)-1:(5 *NBIT)]    = BuildTsData( i_pat_data[5 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[5 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[5]);
assign  int_pat_data[(6 *NBIT)+(NBIT)-1:(6 *NBIT)]    = BuildTsData( i_pat_data[6 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[6 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[6]);
assign  int_pat_data[(7 *NBIT)+(NBIT)-1:(7 *NBIT)]    = BuildTsData( i_pat_data[7 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[7 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[7]);
assign  int_pat_data[(8 *NBIT)+(NBIT)-1:(8 *NBIT)]    = BuildTsData( i_pat_data[8 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[8 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[8]);
assign  int_pat_data[(9 *NBIT)+(NBIT)-1:(9 *NBIT)]    = BuildTsData( i_pat_data[9 *NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[9 * 8 +: 8], lpbk_entry_not_lane_under_test_for_master[9]);
assign  int_pat_data[(10 *NBIT)+(NBIT)-1:(10 *NBIT)]  = BuildTsData( i_pat_data[10*NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[10* 8 +: 8], lpbk_entry_not_lane_under_test_for_master[10]);
assign  int_pat_data[(11 *NBIT)+(NBIT)-1:(11 *NBIT)]  = BuildTsData( i_pat_data[11*NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[11* 8 +: 8], lpbk_entry_not_lane_under_test_for_master[11]);
assign  int_pat_data[(12 *NBIT)+(NBIT)-1:(12 *NBIT)]  = BuildTsData( i_pat_data[12*NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[12* 8 +: 8], lpbk_entry_not_lane_under_test_for_master[12]);
assign  int_pat_data[(13 *NBIT)+(NBIT)-1:(13 *NBIT)]  = BuildTsData( i_pat_data[13*NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[13* 8 +: 8], lpbk_entry_not_lane_under_test_for_master[13]);
assign  int_pat_data[(14 *NBIT)+(NBIT)-1:(14 *NBIT)]  = BuildTsData( i_pat_data[14*NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[14* 8 +: 8], lpbk_entry_not_lane_under_test_for_master[14]);
assign  int_pat_data[(15 *NBIT)+(NBIT)-1:(15 *NBIT)]  = BuildTsData( i_pat_data[15*NBIT +: NBIT],    int_active_nb, insert_s15_4[15],  latched_mod_ts, latched_parity[15* 8 +: 8], lpbk_entry_not_lane_under_test_for_master[15]);



// get bit-wise even parity per lane for Sym15 of Mod TS Format
// latch sym14 - 4 for sym15 bit-wise even parity from base spec 5.0
// insert_s15_4[15:4] = [15] is Sym15, ..., [4] is Sym4
always @(posedge core_clk or negedge core_rst_n) begin : latched_sym15_4_PROC
    integer n;

    if (!core_rst_n) begin
        latched_parity         <= #TP 0;
    end else if ( xmlh_pat_linkloc[0] ) begin // clear when insert link#
        latched_parity         <= #TP 0;
    end else begin
        for ( n=0; n<NL; n=n+1 ) begin
            case ( int_active_nb[4:0] )
                5'b00010  : begin // 2s, bit[15:8]^bit[7:0] on each lane per clock cycle until sym13
                    latched_parity[n*8 +: 8] <= #TP |insert_s15_4[13:4] ? int_pat_data[n*NBIT +: 8]^int_pat_data[(n*NBIT+8) +: 8]^latched_parity[n*8 +: 8] : latched_parity[n*8 +: 8];
                end
                5'b00100  : begin // 4s, bit[31:24]^bit[23:16]^bit[15:8]^bit[7:0] on each lane per clock cycle until sym11
                    latched_parity[n*8 +: 8] <= #TP |insert_s15_4[11:4] ? int_pat_data[n*NBIT +: 8]^int_pat_data[(n*NBIT+8) +: 8]^int_pat_data[(n*NBIT+16) +: 8]^int_pat_data[(n*NBIT+24) +: 8]^latched_parity[n*8 +: 8] : latched_parity[n*8 +: 8];
                end
// no parity for 8s and 16s because max Sness is 4 for Gen1/2 rate
                default   : begin
                    latched_parity[n*8 +: 8] <= #TP latched_parity[n*8 +: 8];
                end
            endcase
        end // for ( n=0; n<NL; n=n+1 )
    end
end // latched_sym15_4_PROC

// Per lane pattern k char
//      pat_datak[(00*NBK)+(NBK)-1:(00*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[00], insert_lanenum[00]  );
assign  int_pat_datak[(0 *PAT_NBK)+(PAT_NBK)-1:(0 *PAT_NBK)]  = InsertKNum( xmlh_pat_datak[(0 *PAT_NBK)+(PAT_NBK)-1:(0 *PAT_NBK)], int_active_nb, insert_linknum[0],  insert_lanenum[0] , lpbk_entry_not_lane_under_test_for_master[0] );
assign  int_pat_datak[(1 *NBK)+(NBK)-1:(1 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[1],  insert_lanenum[1] , lpbk_entry_not_lane_under_test_for_master[1] );
assign  int_pat_datak[(2 *NBK)+(NBK)-1:(2 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[2],  insert_lanenum[2] , lpbk_entry_not_lane_under_test_for_master[2] );
assign  int_pat_datak[(3 *NBK)+(NBK)-1:(3 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[3],  insert_lanenum[3] , lpbk_entry_not_lane_under_test_for_master[3] );
assign  int_pat_datak[(4 *NBK)+(NBK)-1:(4 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[4],  insert_lanenum[4] , lpbk_entry_not_lane_under_test_for_master[4] );
assign  int_pat_datak[(5 *NBK)+(NBK)-1:(5 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[5],  insert_lanenum[5] , lpbk_entry_not_lane_under_test_for_master[5] );
assign  int_pat_datak[(6 *NBK)+(NBK)-1:(6 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[6],  insert_lanenum[6] , lpbk_entry_not_lane_under_test_for_master[6] );
assign  int_pat_datak[(7 *NBK)+(NBK)-1:(7 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[7],  insert_lanenum[7] , lpbk_entry_not_lane_under_test_for_master[7] );
assign  int_pat_datak[(8 *NBK)+(NBK)-1:(8 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[8],  insert_lanenum[8] , lpbk_entry_not_lane_under_test_for_master[8] );
assign  int_pat_datak[(9 *NBK)+(NBK)-1:(9 *NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[9],  insert_lanenum[9] , lpbk_entry_not_lane_under_test_for_master[9] );
assign  int_pat_datak[(10*NBK)+(NBK)-1:(10*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[10], insert_lanenum[10] , lpbk_entry_not_lane_under_test_for_master[10] );
assign  int_pat_datak[(11*NBK)+(NBK)-1:(11*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[11], insert_lanenum[11] , lpbk_entry_not_lane_under_test_for_master[11] );
assign  int_pat_datak[(12*NBK)+(NBK)-1:(12*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[12], insert_lanenum[12] , lpbk_entry_not_lane_under_test_for_master[12] );
assign  int_pat_datak[(13*NBK)+(NBK)-1:(13*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[13], insert_lanenum[13] , lpbk_entry_not_lane_under_test_for_master[13] );
assign  int_pat_datak[(14*NBK)+(NBK)-1:(14*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[14], insert_lanenum[14] , lpbk_entry_not_lane_under_test_for_master[14] );
assign  int_pat_datak[(15*NBK)+(NBK)-1:(15*NBK)]  = InsertKNum( xmlh_pat_datak, int_active_nb, insert_linknum[15], insert_lanenum[15] , lpbk_entry_not_lane_under_test_for_master[15] );


// Per lane EDS data
assign  int_eds_data[(0 *NBIT)+(NBIT)-1:(0 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0000 );
assign  int_eds_data[(1 *NBIT)+(NBIT)-1:(1 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0001 );
assign  int_eds_data[(2 *NBIT)+(NBIT)-1:(2 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0010 );
assign  int_eds_data[(3 *NBIT)+(NBIT)-1:(3 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0011 );
assign  int_eds_data[(4 *NBIT)+(NBIT)-1:(4 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0100 );
assign  int_eds_data[(5 *NBIT)+(NBIT)-1:(5 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0101 );
assign  int_eds_data[(6 *NBIT)+(NBIT)-1:(6 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0110 );
assign  int_eds_data[(7 *NBIT)+(NBIT)-1:(7 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b0111 );
assign  int_eds_data[(8 *NBIT)+(NBIT)-1:(8 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1000 );
assign  int_eds_data[(9 *NBIT)+(NBIT)-1:(9 *NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1001 );
assign  int_eds_data[(10*NBIT)+(NBIT)-1:(10*NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1010 );
assign  int_eds_data[(11*NBIT)+(NBIT)-1:(11*NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1011 );
assign  int_eds_data[(12*NBIT)+(NBIT)-1:(12*NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1100 );
assign  int_eds_data[(13*NBIT)+(NBIT)-1:(13*NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1101 );
assign  int_eds_data[(14*NBIT)+(NBIT)-1:(14*NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1110 );
assign  int_eds_data[(15*NBIT)+(NBIT)-1:(15*NBIT)]  = BuildEDSData( int_active_nb, active_lane_cnt, eds_sym_cnt, 4'b1111 );

// Per Lane SKP OS Data
reg  [NL-1:0] skp_parity;           // Data Parity / ~LFSR[22]. Used in Symbol 13 of SKP OS

// SKP OS Symbol 13, Bit 7
// If prior block was a Data Block
// then this bit carries the even parity for prior Data Blocks for
// a given lane.
// Else, this bit carries ~LFSR[22].
wire [7:0]  eieos_0 = {`EPX16_EIEOS_SYM_0};
wire [7:0]  eios_0  = {`EPX16_EIOS_SYM_0};
wire [7:0]  ts1_0   = {`EPX16_TS1_SYM_0};
wire [7:0]  ts2_0   = {`EPX16_TS2_SYM_0};
always @(posedge core_clk or negedge core_rst_n)
begin : data_stream_window_PROC
    if (!core_rst_n)
        data_stream_window <= #TP 1'b0;
    else if ((xmtbyte_txdatak[1:0] == `EPX16_SYNC_DATA_BLOCK) && xmtbyte_txstartblock[0])
        data_stream_window <= #TP 1'b1;
    else if (((xmtbyte_txdata[7:0] == eios_0) || (xmtbyte_txdata[7:0] == eieos_0) || (xmtbyte_txdata[7:0] == ts1_0) || (xmtbyte_txdata[7:0] == ts2_0)) && (xmtbyte_txdatak[1:0] == `EPX16_SYNC_OS_BLOCK) && xmtbyte_txstartblock[0])
        data_stream_window <= #TP 1'b0;
end

always @(*)
begin : skp_parity_proc
    integer i;
    for (i=0; i<NL; i=i+1) begin
        if (data_stream_window)
            skp_parity[i] = scramble_even_parity[i];
        else
            skp_parity[i] = ~scramble_lfsr_value[(23*i)+22];
    end
end

// Latched margin info not to change while transmitting a Control SKP-OS
reg  [NL*16-1:0]      tx_margin_info;  // Margining Control [2:0] Receiver Number[5:3]Margin Type[6]Usage Molde[7]Rsvd[15:8]Margin Payload
always @(posedge core_clk or negedge core_rst_n)
if (!core_rst_n) begin
    tx_margin_info            <= #TP {NL*16{1'b0}};
end else begin
    if(|xmlh_pat_in_ctlskip) begin
        tx_margin_info        <= #TP tx_margin_info;
    end else begin
        tx_margin_info        <= #TP (cfg_upstream_port) ? smlh_ctlskp_info : cfg_margin_control;
    end
end

assign  skp_margin_sym[(0 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[0 ], tx_margin_info[(0 *16) +: 16]);
assign  skp_margin_sym[(1 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[1 ], tx_margin_info[(1 *16) +: 16]);
assign  skp_margin_sym[(2 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[2 ], tx_margin_info[(2 *16) +: 16]);
assign  skp_margin_sym[(3 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[3 ], tx_margin_info[(3 *16) +: 16]);
assign  skp_margin_sym[(4 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[4 ], tx_margin_info[(4 *16) +: 16]);
assign  skp_margin_sym[(5 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[5 ], tx_margin_info[(5 *16) +: 16]);
assign  skp_margin_sym[(6 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[6 ], tx_margin_info[(6 *16) +: 16]);
assign  skp_margin_sym[(7 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[7 ], tx_margin_info[(7 *16) +: 16]);
assign  skp_margin_sym[(8 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[8 ], tx_margin_info[(8 *16) +: 16]);
assign  skp_margin_sym[(9 *24) +: 24] = BuildSKPMarginSym( scramble_even_parity[9 ], tx_margin_info[(9 *16) +: 16]);
assign  skp_margin_sym[(10*24) +: 24] = BuildSKPMarginSym( scramble_even_parity[10], tx_margin_info[(10*16) +: 16]);
assign  skp_margin_sym[(11*24) +: 24] = BuildSKPMarginSym( scramble_even_parity[11], tx_margin_info[(11*16) +: 16]);
assign  skp_margin_sym[(12*24) +: 24] = BuildSKPMarginSym( scramble_even_parity[12], tx_margin_info[(12*16) +: 16]);
assign  skp_margin_sym[(13*24) +: 24] = BuildSKPMarginSym( scramble_even_parity[13], tx_margin_info[(13*16) +: 16]);
assign  skp_margin_sym[(14*24) +: 24] = BuildSKPMarginSym( scramble_even_parity[14], tx_margin_info[(14*16) +: 16]);
assign  skp_margin_sym[(15*24) +: 24] = BuildSKPMarginSym( scramble_even_parity[15], tx_margin_info[(15*16) +: 16]);

    localparam NB_SYNC_OS_BLOCK    = {{(NB-2){1'b0}},`EPX16_SYNC_OS_BLOCK};
    localparam NB_SYNC_DATA_BLOCK  = {{(NB-2){1'b0}},`EPX16_SYNC_DATA_BLOCK};


wire [NL-1:0]     pre_cmpl_jmp_en;
wire [NL-1:0]     cmpl_jmp_en;
wire [NL*NB-1:0]  cmpl_jmp_datak;

//Generate Jitter Measurment Pattern Enable
// ltssm_gen4_compliance_jmp
//  4'b0001 | lane 0-15
//  4'b0010 | lane 0,8
//  4'b0011 | lane 1,2
//    --
//  4'b1000 | lane 6,14
//  4'b1001 | lane 7,15
assign pre_cmpl_jmp_en[0] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b0010));
assign pre_cmpl_jmp_en[1] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b0011));
assign pre_cmpl_jmp_en[2] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b0100));
assign pre_cmpl_jmp_en[3] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b0101));
assign pre_cmpl_jmp_en[4] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b0110));
assign pre_cmpl_jmp_en[5] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b0111));
assign pre_cmpl_jmp_en[6] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b1000));
assign pre_cmpl_jmp_en[7] = ((ltssm_gen4_compliance_jmp==4'b0001) || (ltssm_gen4_compliance_jmp==4'b1001));
assign pre_cmpl_jmp_en[15:8] = cmpl_jmp_en[7:0];

assign cmpl_jmp_en = pre_cmpl_jmp_en;

//Generate SyncHeader for Jitter Measurment Pattern
assign cmpl_jmp_datak[NB*0 +: NB] = (cmpl_jmp_en[0] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*1 +: NB] = (cmpl_jmp_en[1] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*2 +: NB] = (cmpl_jmp_en[2] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*3 +: NB] = (cmpl_jmp_en[3] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*4 +: NB] = (cmpl_jmp_en[4] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*5 +: NB] = (cmpl_jmp_en[5] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*6 +: NB] = (cmpl_jmp_en[6] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*7 +: NB] = (cmpl_jmp_en[7] || xmlh_cmpl_os_sync_header[0]) ? NB_SYNC_OS_BLOCK : NB_SYNC_DATA_BLOCK;
assign cmpl_jmp_datak[NB*8 +: NB*8] = cmpl_jmp_datak[NB*0 +: NB*8];


assign  int_skp_data[(1 *PAT_NBIT)-1:(0 *PAT_NBIT)] = BuildSKPData(xmlh_pat_data[(1 *PAT_NBIT)-1:(0 *PAT_NBIT)],skp_parity[0 ],scramble_lfsr_value[(1 *23)-1:0 *23],smlh_gen3_error_count[(1 *8)-1:0 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[0 *24 +: 24]);
assign  int_skp_data[(2 *NBIT)-1:(1 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[1 ],scramble_lfsr_value[(2 *23)-1:1 *23],smlh_gen3_error_count[(2 *8)-1:1 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[1 *24 +: 24]);
assign  int_skp_data[(3 *NBIT)-1:(2 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[2 ],scramble_lfsr_value[(3 *23)-1:2 *23],smlh_gen3_error_count[(3 *8)-1:2 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[2 *24 +: 24]);
assign  int_skp_data[(4 *NBIT)-1:(3 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[3 ],scramble_lfsr_value[(4 *23)-1:3 *23],smlh_gen3_error_count[(4 *8)-1:3 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[3 *24 +: 24]);
assign  int_skp_data[(5 *NBIT)-1:(4 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[4 ],scramble_lfsr_value[(5 *23)-1:4 *23],smlh_gen3_error_count[(5 *8)-1:4 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[4 *24 +: 24]);
assign  int_skp_data[(6 *NBIT)-1:(5 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[5 ],scramble_lfsr_value[(6 *23)-1:5 *23],smlh_gen3_error_count[(6 *8)-1:5 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[5 *24 +: 24]);
assign  int_skp_data[(7 *NBIT)-1:(6 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[6 ],scramble_lfsr_value[(7 *23)-1:6 *23],smlh_gen3_error_count[(7 *8)-1:6 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[6 *24 +: 24]);
assign  int_skp_data[(8 *NBIT)-1:(7 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[7 ],scramble_lfsr_value[(8 *23)-1:7 *23],smlh_gen3_error_count[(8 *8)-1:7 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[7 *24 +: 24]);
assign  int_skp_data[(9 *NBIT)-1:(8 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[8 ],scramble_lfsr_value[(9 *23)-1:8 *23],smlh_gen3_error_count[(9 *8)-1:8 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[8 *24 +: 24]);
assign  int_skp_data[(10*NBIT)-1:(9 *NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[9 ],scramble_lfsr_value[(10*23)-1:9 *23],smlh_gen3_error_count[(10*8)-1:9 *8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[9 *24 +: 24]);
assign  int_skp_data[(11*NBIT)-1:(10*NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[10],scramble_lfsr_value[(11*23)-1:10*23],smlh_gen3_error_count[(11*8)-1:10*8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[10*24 +: 24]);
assign  int_skp_data[(12*NBIT)-1:(11*NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[11],scramble_lfsr_value[(12*23)-1:11*23],smlh_gen3_error_count[(12*8)-1:11*8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[11*24 +: 24]);
assign  int_skp_data[(13*NBIT)-1:(12*NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[12],scramble_lfsr_value[(13*23)-1:12*23],smlh_gen3_error_count[(13*8)-1:12*8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[12*24 +: 24]);
assign  int_skp_data[(14*NBIT)-1:(13*NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[13],scramble_lfsr_value[(14*23)-1:13*23],smlh_gen3_error_count[(14*8)-1:13*8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[13*24 +: 24]);
assign  int_skp_data[(15*NBIT)-1:(14*NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[14],scramble_lfsr_value[(15*23)-1:14*23],smlh_gen3_error_count[(15*8)-1:14*8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[14*24 +: 24]);
assign  int_skp_data[(16*NBIT)-1:(15*NBIT)] = BuildSKPData(xmlh_pat_data,skp_parity[15],scramble_lfsr_value[(16*23)-1:15*23],smlh_gen3_error_count[(16*8)-1:15*8],int_active_nb,xmt_sym_cnt,ltssm_in_compliance ,xmlh_pat_in_ctlskip[0],skp_margin_sym[15*24 +: 24]);

always @( posedge core_clk or negedge core_rst_n ) begin : int_skp_data_d_PROC
    if ( ~core_rst_n )
        int_skp_data_d <= #TP 0;
    else
        int_skp_data_d <= #TP int_skp_data;
end // int_skp_data_d_PROC

assign  int_ieds_data[(1 *NBIT)-1:(0 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd0);
assign  int_ieds_data[(2 *NBIT)-1:(1 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd1);
assign  int_ieds_data[(3 *NBIT)-1:(2 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd2);
assign  int_ieds_data[(4 *NBIT)-1:(3 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd3);
assign  int_ieds_data[(5 *NBIT)-1:(4 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd4);
assign  int_ieds_data[(6 *NBIT)-1:(5 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd5);
assign  int_ieds_data[(7 *NBIT)-1:(6 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd6);
assign  int_ieds_data[(8 *NBIT)-1:(7 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd7);
assign  int_ieds_data[(9 *NBIT)-1:(8 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd8);
assign  int_ieds_data[(10*NBIT)-1:(9 *NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd9);
assign  int_ieds_data[(11*NBIT)-1:(10*NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd10);
assign  int_ieds_data[(12*NBIT)-1:(11*NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd11);
assign  int_ieds_data[(13*NBIT)-1:(12*NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd12);
assign  int_ieds_data[(14*NBIT)-1:(13*NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd13);
assign  int_ieds_data[(15*NBIT)-1:(14*NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd14);
assign  int_ieds_data[(16*NBIT)-1:(15*NBIT)] = BuildiEDSData(int_link_mode, int_active_nb, xmt_sym_cnt, 4'd15);

//Build up Compliance data
//gates: Tx preset is lane-based.
  assign  int_cmpl_data[(1 *NBIT)-1:(0 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h0, eqctl_pset_ltx[1*TX_PSET_WD-1:TX_PSET_WD*0],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[0]);
assign  int_cmpl_data[(2 *NBIT)-1:(1 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h1, eqctl_pset_ltx[2*TX_PSET_WD-1:TX_PSET_WD*1],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[1]);
assign  int_cmpl_data[(3 *NBIT)-1:(2 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h2, eqctl_pset_ltx[3*TX_PSET_WD-1:TX_PSET_WD*2],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[2]);
assign  int_cmpl_data[(4 *NBIT)-1:(3 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h3, eqctl_pset_ltx[4*TX_PSET_WD-1:TX_PSET_WD*3],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[3]);
assign  int_cmpl_data[(5 *NBIT)-1:(4 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h4, eqctl_pset_ltx[5*TX_PSET_WD-1:TX_PSET_WD*4],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[4]);
assign  int_cmpl_data[(6 *NBIT)-1:(5 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h5, eqctl_pset_ltx[6*TX_PSET_WD-1:TX_PSET_WD*5],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[5]);
assign  int_cmpl_data[(7 *NBIT)-1:(6 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h6, eqctl_pset_ltx[7*TX_PSET_WD-1:TX_PSET_WD*6],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[6]);
assign  int_cmpl_data[(8 *NBIT)-1:(7 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h7, eqctl_pset_ltx[8*TX_PSET_WD-1:TX_PSET_WD*7],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[7]);
assign  int_cmpl_data[(9 *NBIT)-1:(8 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h0, eqctl_pset_ltx[9*TX_PSET_WD-1:TX_PSET_WD*8],   int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[8]);
assign  int_cmpl_data[(10*NBIT)-1:(9 *NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h1, eqctl_pset_ltx[10*TX_PSET_WD-1:TX_PSET_WD*9],  int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[9]);
assign  int_cmpl_data[(11*NBIT)-1:(10*NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h2, eqctl_pset_ltx[11*TX_PSET_WD-1:TX_PSET_WD*10], int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[10]);
assign  int_cmpl_data[(12*NBIT)-1:(11*NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h3, eqctl_pset_ltx[12*TX_PSET_WD-1:TX_PSET_WD*11], int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[11]);
assign  int_cmpl_data[(13*NBIT)-1:(12*NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h4, eqctl_pset_ltx[13*TX_PSET_WD-1:TX_PSET_WD*12], int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[12]);
assign  int_cmpl_data[(14*NBIT)-1:(13*NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h5, eqctl_pset_ltx[14*TX_PSET_WD-1:TX_PSET_WD*13], int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[13]);
assign  int_cmpl_data[(15*NBIT)-1:(14*NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h6, eqctl_pset_ltx[15*TX_PSET_WD-1:TX_PSET_WD*14], int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[14]);
assign  int_cmpl_data[(16*NBIT)-1:(15*NBIT)] = BuildG3CmpData( xmlh_pat_data, 3'h7, eqctl_pset_ltx[16*TX_PSET_WD-1:TX_PSET_WD*15], int_active_nb, xmt_sym_cnt, xmlh_cmpl_block_count[1:0] , cmpl_jmp_en[15]);


// This optional register is currently not needed
// Optional Register
//parameter N_DELAY_CYLES     = REGOUT ? 1 : 0;
//parameter DATAPATH_WIDTH    = (NB*8*NL) + (NB*NL);
//delay_n #(N_DELAY_CYLES, DATAPATH_WIDTH) u0_delay(
//    .clk        (core_clk),
//    .rst_n      (core_rst_n),
//    .clear      (1'b0),
//    .din        ({ int_xmt_data, int_xmt_datak }),
//    .dout       ({ xmt_data,     xmt_datak     })
//);
assign  xmt_data    = (xbyte_curnt_state == S_CMP)      ?
                                                          (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? (latched_in_mod_compliance ?
                                                                                                            (xmlh_mod_cmpl_skp[0] ? int_skp_data : {NL{xmlh_pat_data}}) : int_cmpl_data) :
                                                          cmp_data               :
                      // the null flit length can be variable for implied EDS insertion just before the EIEOS/EIOS Block
                      (xbyte_curnt_state == S_EDS)      ? (ltssm_in_lpbkentry & lpbk_master) ? 0 : cxl_mode_enable ? int_xmt_data : int_eds_data : // no Tx eds in Loopback.Entry master, assign to 0 for smooth transition to sending OS
                      (valid_pkt_in_progress_i)         ? int_xmt_data :
                                                          {DW{1'b0}}             ;
assign  xmt_datak   = (xbyte_curnt_state == S_CMP)      ?
                                                          (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? 
                                                                                                                                 (|cmpl_jmp_en) ? cmpl_jmp_datak : 
                                                                                                                                 (xmlh_cmpl_os_sync_header[0] ? {NL{{(NB-2){1'b0}},`EPX16_SYNC_OS_BLOCK}} : {NL{{(NB-2){1'b0}},`EPX16_SYNC_DATA_BLOCK}}) :
                                                          cmp_datak              :
                      // repurpose txdatak to carry txsyncheader
                      (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? {NL{datablock_syncheader}} :
                      (valid_pkt_in_progress_i)         ? int_xmt_datak          :
                                                          {NBYTE{1'b0}}          ;

// if ltssm_cmd == `EPX16_XMT_IN_EIDLE, transmitter shoud be idle 0s
assign  pat_data    = (ltssm_cmd == `EPX16_XMT_IN_EIDLE)      ? {DW{1'b0}}             :
                      (
                       (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) &&
                       (xbyte_curnt_state == S_SKIP))   ? (null_ieds_for_skip_sent ? int_ieds_data :

                        int_skp_data) :
                                                          int_pat_data           ;
assign  pat_datak   = (ltssm_cmd == `EPX16_XMT_IN_EIDLE)      ? {NBYTE{1'b0}}          :
                      // repurpose txdatak to carry txsyncheader
                      (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? (null_ieds_for_skip_sent ? {NL{datablock_syncheader}} : {NL{osblock_syncheader}})   :
                                                          int_pat_datak          ;
// Choose appropriate TxSyncHeader value when !cmd_is_data && !xmlh_pat_dv
assign txsyncheader  = ( xbyte_curnt_state == S_IDLE )  ? {NL{datablock_syncheader}} :
                                                          {NL{osblock_syncheader}}   ;


// =============================================================================
// Output Data Mux and register
// =============================================================================

always @(posedge core_clk or negedge core_rst_n)
begin : output_data_proc
    integer i;
    if (!core_rst_n) begin
        int_xmtbyte_txdata             <= #TP {DW{1'b0}};
        int_xmtbyte_txdatak            <= #TP {NBYTE{1'b0}};
        xmtbyte_link_in_training   <= #TP 1'b0 ;
    end else if(int_eidle_i) begin
        int_xmtbyte_txdata             <= #TP {DW{1'b0}};
        int_xmtbyte_txdatak            <= #TP {NBYTE{1'b0}};
        xmtbyte_link_in_training   <= #TP int_in_training;
    end else begin
        if (cmd_is_data
            || (xbyte_curnt_state == S_EDS)
            || (xbyte_curnt_state == S_CMP)) begin
            if ( lpbk_master && ltssm_in_lpbk_active && cfg_prbs_en )begin
              int_xmtbyte_txdata         <= #TP 0;
              if ( current_data_rate < `EPX16_GEN3_RATE )begin
                int_xmtbyte_txdatak        <= #TP 0;
              end else begin
                int_xmtbyte_txdatak        <= #TP
                                               xmt_datak;
              end
            end else begin
              int_xmtbyte_txdata         <= #TP xmt_data;
              int_xmtbyte_txdatak        <= #TP 

                                               xmt_datak;
            end
        end else if (xmlh_pat_dv) begin
            int_xmtbyte_txdata         <= #TP pat_data;
            int_xmtbyte_txdatak        <= #TP pat_datak;
        end else begin
            int_xmtbyte_txdata         <= #TP {DW{1'b0}};
            int_xmtbyte_txdatak        <= #TP (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? txsyncheader : {NBYTE{1'b0}};
        end
        xmtbyte_link_in_training   <= #TP int_in_training;
    end
end

assign xmtbyte_txdata  = int_xmtbyte_txdata  ;
assign xmtbyte_txdatak = int_xmtbyte_txdatak ;


parameter N_DELAY_CYLES2    = `EPX16_CX_XMLH_SCRAMBLE_REGOUT;
parameter RESETVAL  = {NL{1'b1}};
EPX16_delay_n

#(N_DELAY_CYLES2, NL, RESETVAL) u1_delay(
    .clk        (core_clk),
    .rst_n      (core_rst_n),
    .clear      (1'b0),
    .din        (xmtbyte_txelecidle_i),
    .dout       (xmtbyte_txelecidle_g12)
);

localparam G3_DELAY_CYCLES = `EPX16_CX_XMLH_GEN3_SCRAMBLE_SKIP_ALIGN + `EPX16_CX_XMLH_GEN3_SCRAMBLE_LFSR_REGOUT + `EPX16_CX_XMLH_GEN3_SCRAMBLE_REGIN + `EPX16_CX_XMLH_GEN3_SCRAMBLE_REGOUT - N_DELAY_CYLES2 + `EPX16_CX_XMLH_PRECODING_REGOUT;
wire    [NL-1:0]        pre_xmtbyte_txelecidle_g3;
wire    [NL-1:0]        tmp_xmtbyte_txelecidle_g3;

generate if (G3_DELAY_CYCLES > 0) begin : gen_delay_txelecidle_1
EPX16_delay_n

#(G3_DELAY_CYCLES - 1, NL, RESETVAL) u2_delay(
    .clk        (core_clk),
    .rst_n      (core_rst_n),
    .clear      (1'b0),
    .din        (xmtbyte_txelecidle_g12),
    .dout       (pre_xmtbyte_txelecidle_g3)
);
EPX16_delay_n

#(1 , NL, RESETVAL) u2_delay_g3(
    .clk        (core_clk),
    .rst_n      (core_rst_n),
    .clear      (1'b0),
    .din        (pre_xmtbyte_txelecidle_g3),
    .dout       (tmp_xmtbyte_txelecidle_g3)
);
// Aligned assertion timing with mac_phy_txdetectrx_loopback signal on pipe_if
assign xmtbyte_txelecidle_g3 = (ltssm_in_lpbk_i && !lpbk_master && (ltssm_cmd == `EPX16_XMT_IN_EIDLE && current_data_rate >= `EPX16_GEN5_RATE) && G3_DELAY_CYCLES>=2) ? pre_xmtbyte_txelecidle_g3 : tmp_xmtbyte_txelecidle_g3;
end else begin : gen_delay_txelecidle_0
assign pre_xmtbyte_txelecidle_g3 = xmtbyte_txelecidle_i;
assign xmtbyte_txelecidle_g3 = xmtbyte_txelecidle_g12;
end endgenerate

assign xmtbyte_txelecidle = (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? xmtbyte_txelecidle_g3 : xmtbyte_txelecidle_g12;

assign xmtbyte_g5_lpbk_deassert = ((current_data_rate == `EPX16_GEN5_RATE)) & (&pre_xmtbyte_txelecidle_g3) & (smlh_ltssm_state ==  `EPX16_S_LPBK_EXIT_TIMEOUT);

//pipeline for txcompliance to align txdata and txelecidle
parameter RESET_VALUE = {NL{1'b0}};
EPX16_delay_n

#(N_DELAY_CYLES2, NL, RESET_VALUE) txcompl_delay(
    .clk        (core_clk),
    .rst_n      (core_rst_n),
    .clear      (1'b0),
    .din        (xmtbyte_txcompliance_i),
    .dout       (xmtbyte_txcompliance_g12)
);

EPX16_delay_n

#(G3_DELAY_CYCLES, NL, RESET_VALUE) txcompl_g3_delay(
    .clk        (core_clk),
    .rst_n      (core_rst_n),
    .clear      (1'b0),
    .din        (xmtbyte_txcompliance_g12),
    .dout       (xmtbyte_txcompliance_g3)
);
assign xmtbyte_txcompliance = (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? xmtbyte_txcompliance_g3 : xmtbyte_txcompliance_g12;

//pipeline for loopback to align txdata and txelecidle
parameter RST_VALUE = 1'b0;

EPX16_delay_n

#(G3_DELAY_CYCLES, 1, RST_VALUE) loopback_g3_delay(
    .clk        (core_clk),
    .rst_n      (core_rst_n),
    .clear      (1'b0),
    .din        (ltssm_in_lpbk),
    .dout       (ltssm_in_lpbk_g3)
);
assign ltssm_in_lpbk_i = (current_data_rate == `EPX16_GEN3_RATE || current_data_rate == `EPX16_GEN4_RATE || current_data_rate == `EPX16_GEN5_RATE) ? ltssm_in_lpbk_g3 : ltssm_in_lpbk;

// =============================================================================
// Pattern Generator
// =============================================================================
wire poll_cmp_enter_act_pulse;
wire pre_poll_cmp_enter_act_pulse = (xbyte_curnt_state == S_CMP) & (smlh_ltssm_state == `EPX16_S_POLL_ACTIVE) & (next_pat == `EPX16_COMPLIANCE_PATTERN) & ltssm_clear;
assign poll_cmp_enter_act_pulse = pre_poll_cmp_enter_act_pulse;

wire load_pat_cmp = (ltssm_cmd == `EPX16_COMPLIANCE_PATTERN || ltssm_cmd == `EPX16_MOD_COMPL_PATTERN) & load_pat_i;
EPX16_xmlh_pat_gen

u_xmlh_pat_gen (
    .core_clk                   (core_clk),
    .core_rst_n                 (core_rst_n),

    .poll_cmp_enter_act_pulse   (poll_cmp_enter_act_pulse),
    .app_sris_mode              (app_sris_mode),
    .cfg_n_fts                  (cfg_n_fts),
  `ifndef SYNTHESIS
    .smlh_ltssm_state           (smlh_ltssm_state),
  `endif // SYNTHESIS

    .next_pat                   (next_pat),
    .next_ctlskip_pat           (next_ctlskip_pat),
    .load_pat_cmp               (load_pat_cmp),
    .load_pat                   (load_pat),
    .ltssm_ts_halt              (ltssm_ts_halt),
    .state_halt                 (state_halt),
    .ltssm_ts_cntrl             (latched_ts_cntrl),
    .ltssm_mod_ts               (latched_mod_ts),
    .ltssm_ts_alt_protocol      (latched_ts_alt_protocol),
    .ltssm_ts_auto_change       (latched_ts_auto_change_i),
    .ltssm_ts_alt_prot_info     (latched_alt_prot_info),
    .ltssm_ts_data_rate         (latched_ts_data_rate_i),
    .ltssm_ts_speed_change      (ltssm_ts_speed_change),
    .current_data_rate          (current_data_rate),
    .active_nb                  (int_active_nb[AW-1:0]),
    .xmtbyte_idle_sent          (xmtbyte_idle_sent),
    .xmtbyte_eidle_sent         (xmtbyte_eidle_sent),

    // ---------------------------------outputs ------------------------
    .os_start                   (os_start),
    .os_end                     (os_end),
    .xmlh_pat_ack               (xmlh_pat_ack),
    .xmlh_pat_dv                (xmlh_pat_dv),
    .xmlh_pat_data              (xmlh_pat_data[0 +: PAT_NBIT]),
    .xmlh_pat_datak             (xmlh_pat_datak[0 +: PAT_NBK]),
    .xmlh_pat_linkloc           (xmlh_pat_linkloc[0]),
    .xmlh_pat_laneloc           (xmlh_pat_laneloc[0]),
    .xmlh_pat_eqloc             (xmlh_pat_eqloc),
    .xmlh_pat_eieosloc          (xmlh_pat_eieosloc),
    .xmlh_pat_s15_4loc          (xmlh_pat_s15_4loc),
    .xmlh_cmp_errloc            (xmlh_cmp_errloc),

    .xmlh_dly_cmp_data          (xmlh_dly_cmp_data[0 +: PAT_NBIT]),
    .xmlh_dly_cmp_datak         (xmlh_dly_cmp_datak[0 +: PAT_NBK]),
    .xmlh_dly_cmp_errloc        (xmlh_dly_cmp_errloc),

    .xmtbyte_eies_sent          (pre_xmtbyte_eies_sent[0]),
    .xmtbyte_ts1_sent           (xmtbyte_ts1_sent[0]),
    .xmtbyte_ts2_sent           (xmtbyte_ts2_sent[0]),
    .xmlh_pat_fts_sent          (xmlh_pat_fts_sent),
    .xmlh_pat_eidle_sent        (xmlh_pat_eidle_sent[0]),
    .xmlh_cmpl_os_sync_header   (xmlh_cmpl_os_sync_header[0]),
    .xmlh_cmpl_block_count      (xmlh_cmpl_block_count[1:0]),
    .xmlh_mod_cmpl_skp          (xmlh_mod_cmpl_skp[0]),
    .xmlh_pat_in_ctlskip        (xmlh_pat_in_ctlskip[0]),
    .xmtbyte_skip_sent          (pre_xmtbyte_skip_sent[0])

);


assign xmtbyte_eies_sent = |pre_xmtbyte_eies_sent;
assign xmtbyte_skip_sent = |pre_xmtbyte_skip_sent;
    

// =============================================================================
// Functions
// =============================================================================

// Function to get a Lanes worth of data from the data stream
function automatic [NBIT-1:0] GetSlice;
  input  [DW-1:0]   data;
  input  [SLICE_WD-1:0] slice;
  input  [5:0]      active_nb;
  input  [4:0]      active_lane_cnt;    // this input not used in 1s only mode
  input             lane_enable;

  reg    [SLICE_WD-1:0] tmp_slice[1:NB-1];
begin
    GetSlice = {NBIT{1'b0}};
    if (lane_enable) begin // first byte
        GetSlice[7:0]   = Bget( data, slice);
    end

    if ((|active_nb[4:1]) && lane_enable) begin // second byte
// spyglass disable_block W164a
// SMD: Identifies assignments in which the LHS width is less than the RHS width
// SJ: The rule reports assignments where the result of an addition or subtraction operation is being assigned to a bus of the same width as the operands of the addition or subtraction operation. In this code, the carry or borrow bit is considered and isn't lost. So, disable SpyGlass from reporting this warning.
        tmp_slice[1] = (slice + active_lane_cnt);
// spyglass enable_block W164a
        GetSlice[15:8]  = Bget( data, tmp_slice[1]);
    end

    if ((|active_nb[4:2]) & lane_enable) begin // third & forth byte
// spyglass disable_block W164a
// SMD: Identifies assignments in which the LHS width is less than the RHS width
// SJ: The rule reports assignments where the result of an addition or subtraction operation is being assigned to a bus of the same width as the operands of the addition or subtraction operation. In this code, the carry or borrow bit is considered and isn't lost. So, disable SpyGlass from reporting this warning.
        tmp_slice[2] = (slice + (active_lane_cnt << 1));
// spyglass enable_block W164a
        tmp_slice[3] = (slice + (active_lane_cnt *  3));
        GetSlice[23:16] = Bget( data, tmp_slice[2]);    // shift by one instead of multiply by 2
        GetSlice[31:24] = Bget( data, tmp_slice[3]);
    end



end
endfunction // GetSlice


// Function to get a Lanes worth of data kchar indicators from the data stream
function automatic [NBK-1:0]  GetKSlice;
  input  [NBYTE-1:0]datak;
  input  [SLICE_WD-1:0] slice;
  input  [5:0]      active_nb;
  input  [4:0]      active_lane_cnt;    // this input not used in 1s only mode
  input             lane_enable;

  reg    [SLICE_WD-1:0] tmp_slice[1:NB-1];
begin
    GetKSlice = {NBK{1'b0}};
    if (lane_enable) begin // first byte
        GetKSlice[0] = datak[slice[SLICEK_WD-1:0]];
    end

    if ((|active_nb[4:1]) && lane_enable) begin // second byte
// spyglass disable_block W164a
// SMD: Identifies assignments in which the LHS width is less than the RHS width
// SJ: The rule reports assignments where the result of an addition or subtraction operation is being assigned to a bus of the same width as the operands of the addition or subtraction operation. In this code, the carry or borrow bit is considered and isn't lost. So, disable SpyGlass from reporting this warning.
        tmp_slice[1]    = (slice[SLICEK_WD-1:0] + active_lane_cnt);
// spyglass enable_block W164a
        GetKSlice[1]    = datak[tmp_slice[1][SLICEK_WD-1:0]];
    end

    if ((|active_nb[4:2]) && lane_enable) begin // third & forth byte
// spyglass disable_block W164a
// SMD: Identifies assignments in which the LHS width is less than the RHS width
// SJ: The rule reports assignments where the result of an addition or subtraction operation is being assigned to a bus of the same width as the operands of the addition or subtraction operation. In this code, the carry or borrow bit is considered and isn't lost. So, disable SpyGlass from reporting this warning.
        tmp_slice[2]    = (slice[SLICEK_WD-1:0] + (active_lane_cnt << 1));   // shift by one instead of multiply by 2
// spyglass enable_block W164a
        tmp_slice[3]    = (slice[SLICEK_WD-1:0] + (active_lane_cnt *  3));
        GetKSlice[2]    = datak[tmp_slice[2][SLICEK_WD-1:0]];   // shift by one instead of multiply by 2
        GetKSlice[3]    = datak[tmp_slice[3][SLICEK_WD-1:0]];
    end

end
endfunction // GetKSlice

// Function to replace k char for link and lane numbers in TS ordered sets on a given lane.
function automatic [PAT_NBK-1:0]  InsertKNum;
  input  [PAT_NBK-1:0]  in_datak;               // Lane datak in
  input  [5:0]      active_nb;                  // active number of symbols. bit0=1s, bit1=2s, bit2=4s, bit3=8s
  input             linkloc;                    // Indicates the current data contains the link # field. Its location can be inferred from active_nb
  input             laneloc;                    // Indicates the current data contains the lane # field. Its location can be inferred from active_nb
  input             lut_n;                      // not the lane under test, assign to 0 in Loopback.entry at gen1/2 rate for lpbk master to go eq

  reg    [PAT_NBK-1:0]  InsertKNum_i;
begin
    InsertKNum_i = in_datak;

         case ({active_nb[4:0], linkloc, laneloc})
        7'b00010_10 : InsertKNum_i[1]    = 1'b0;
        7'b00010_01 : InsertKNum_i[0]    = 1'b0;
        7'b00100_10 : InsertKNum_i[1]    = 1'b0;
        7'b00100_01 : InsertKNum_i[2]    = 1'b0;
        7'b00100_11 : InsertKNum_i[2:1]  = 2'b00;
        default   : InsertKNum_i = in_datak;
    endcase

    InsertKNum = lut_n ? {PAT_NBK{1'b0}} : InsertKNum_i;
end
endfunction // InsertKNum


// Function to build a lanes compliance data
function automatic [NBIT-1:0] BuildCmpData;
  input  [NBIT-1:0] in_data;                    // Compliance Data
  input  [NBIT-1:0] dly_in_data;                // Delayed Compliance Data
  input  [6:0]      error_cnt;                  // Error Count
  input             pattern_lock;               // Pattern lock bit
  input  [5:0]      active_nb;                  // active number of symbols. bit0=1s, bit1=2s, bit2=4s,bit3=8s,bit4=16s
  input             dly_lane;                   // Indicates this lane should use delayed Compliance data
  input             cmploc;                     // Indicates the current data contains the compliance error count field. Its location can be inferred from active_nb
  input             dlyloc;                     // Indicates the current data contains the compliance error count field. Its location can be inferred from active_nb

  reg    [7:0]      err_field;                  // the error count and pattern lock combined
  reg               loc;                        // the location to insert error
begin
    BuildCmpData    = dly_lane ? dly_in_data : in_data;
    loc             = (dly_lane) ? dlyloc : cmploc;
    err_field       = {pattern_lock, error_cnt};

    case ({active_nb[4:0], loc})
        6'b00010_1 : BuildCmpData[15:0]  = {2{err_field}};
        6'b00100_1 : BuildCmpData[15:0]  = {2{err_field}};
        default : BuildCmpData        = dly_lane ? dly_in_data : in_data;
    endcase
end
endfunction // InsertCmpErr


// Function to replace link and lane numbers in TS ordered sets on a given lane.
// This function is also used to insert per lane equalization information for
// Symbols 6-9 of a TS Ordered Set and to mask OS Identifier of EIEOS when
// required
function automatic [PAT_NBIT-1:0]     BuildOsData;
  input  [PAT_NBIT-1:0]     in_data;        // Lane data in
  input  [7:0]              linknum;        // Link Number
  input  [7:0]              lanenum;        // Lane Number
  input  [5:0]              active_nb;      // active number of symbols. bit0=1s, bit1=2s, bit2=4s, bit3=8s,bit4=16s
  input                     linkloc;        // Indicates the current data contains the link # field. Its location can be inferred from active_nb
  input                     laneloc;        // Indicates the current data contains the lane # field. Its location can be inferred from active_nb
  input  [3:0]              eqloc;          // Indicates the current data contains one or more of Symbols 6-9 of a TS. for gen6 the active_nb = 8, eqloc[3:0] = 4'b1111 means half OS replaced for TS0. No need change
  input  [EQTS_WD-1:0]      eqinfo;         // Equalization Information for Symbols 6-9 of EQ TSs and Gen3 TSs
  input                     eieosloc;       // Indicates the current data contains the EIEOS OS Identifier and should be replaced with TS1 OS Identifier

begin
    BuildOsData = in_data;
  begin
         case ({active_nb[4:0], linkloc, laneloc, eqloc, eieosloc})
        12'b00010_10_0000_0 : BuildOsData[15:8]  = linknum;       // Symbol 1 Link Number
        12'b00010_01_0000_0 : BuildOsData[7:0]   = lanenum;       // Symbol 2 Lane Number
        12'b00010_00_0011_0 : BuildOsData[15:0]  = eqinfo[15:0];  // Symbol 7 Symbol 6 EQ Data
        12'b00010_00_1100_0 : BuildOsData[15:0]  = eqinfo[31:16]; // Symbol 9 Symbol 8 EQ Data
        12'b00010_00_0000_1 : BuildOsData[7:0]   = `EPX16_TS1_SYM_0;    // Symbol 0 EIEOS OS Identifier.
        12'b00100_10_0000_0 : BuildOsData[15:8]  = linknum;              // Symbol 1 Link Number, g6 eq B0
//      12'b00100_01_0000_0 : BuildOsData[23:16] = lanenum;                                              // Do not insert Lane Number when Link Number is PAD. g6 eq B1
        12'b00100_11_0000_0 : BuildOsData[23:8]  = {lanenum, linknum};  // Symbol 2 Lane Number, Symbol 1 Link Number, g6 eq B1
        12'b00100_00_0011_0 : BuildOsData[31:16] = eqinfo[15:0];                                         // Symbol 7 Symbol 6 EQ Data
        12'b00100_00_1100_0 : BuildOsData[15:0]  = eqinfo[31:16];                                        // Symbol 9 Symbol 8 EQ Data
        12'b00100_00_0000_1 : BuildOsData[7:0]   = `EPX16_TS1_SYM_0;    // Symbol 0 EIEOS OS Identifier.
        default  : BuildOsData = in_data;
    endcase
  end
end
endfunction // BuildOsData

// Function to replace Sym15 for Modified TS Format
function automatic [NBIT-1:0]         BuildTsData;
  input  [NBIT-1:0]         in_data;        // Lane data in
  input  [5:0]              active_nb;      // active number of symbols. bit0=1s, bit1=2s, bit2=4s, bit3=8s,bit4=16s
  input                     insert_15;      // replace sym15
  input                     mod_ts;         // Modified TS supported
  input  [7:0]              latched_parity; // parity until previous clock
  input                     lut_n;          // not lane under test for lpbk master in lpbk.entry at Gen1/2 rate sending TS1s

  reg    [NBIT-1:0]         BuildTsData_i;
begin
    BuildTsData_i = in_data;

           case ({active_nb[4:0], insert_15})
        6'b00010_1 : BuildTsData_i[15:8]  = mod_ts ? in_data[7:0]^latched_parity : in_data[15:8];                               //if Modified TS, sym15 = previous-even-parity^bit[7:0]. Else, keep as is
        6'b00100_1 : BuildTsData_i[31:24] = mod_ts ? in_data[23:16]^in_data[15:8]^in_data[7:0]^latched_parity : in_data[31:24]; //if Modified TS, sym15 = previous-even-parity^bit[7:0]^bit[15:8]^bit[23:16]. Else, keep as is
// no replacement for 8s and 16s because max Sness is 4 for 8b/10b coding
        default  : BuildTsData_i = in_data;
    endcase

    BuildTsData = lut_n ? {NBIT{1'b0}} : BuildTsData_i; //if not the lane under test, assign 0 in loopback.entry at Gen1/2 rate for loopback master
end
endfunction // BuildTsData

// Function to insert EDS Token.
// 1DW long EDS Token is inserted based on current S-ness, current number of
// active lanes, current symbol in 128-bit block and current lane.

/*
Table below shows the mapping of EDS byte numbers to the output datapath [DW-1:0] as a function of s-ness, link width and symbol count within a block. 
Each column on the left corresponds to the RTL code for a given lane. The 8sx8 and 4sx16 cases are specific to 512b. 



                                                                                                                               lane#
                                     |     15             14             13             12    ....      7              6              5              4              3              2              1              0
   s-ness xN  sym_c      EDS byte#   |
-------------------------------------|---------------------------------------------------------||----------------------------------------------------------------------------------------------------------------------
                                     |                                                                                                                                                                                 
     1s     x1  12                   |                                                                                                                                                                          (0)    
     1s     x1  13                   |                                                                                                                                                                          (1)    
     1s     x1  14                   |                                                                                                                                                                          (2)    
     1s     x1  15                   |                                                                                                                                                                          (3)    
     1s     x2  14                   |                                                                                                                                                           (1)            (0)    
     1s     x2  15                   |                                                                                                                                                           (3)            (2)    
     1s     x4  15                   |                                                                                                                             (3)            (2)            (1)            (0)    
     1s     x8  15                   |                                                                 (3)            (2)            (1)            (0)
     1s     x16 15                   |     (3)            (2)            (1)            (0)
     2s     x1  12                   |                                                                                                                                                                         (10)    
     2s     x1  14                   |                                                                                                                                                                         (32)    
     2s     x2  14                   |                                                                                                                                                          (31)           (20)    
     2s     x4  14                   |                                                                                                                            (3-)           (2-)           (1-)           (0-)    
     2s     x8  14                   |                                                                (3-)           (2-)           (1-)           (0-)
     2s     x16 14                   |    (3-)           (2-)           (1-)           (0-)
     4s     x1  12                   |                                                                                                                                                                       (3210)    
     4s     x2  12                   |                                                                                                                                                        (31--)         (20--)    
     4s     x4  12                   |                                                                                                                          (3---)         (2---)         (1---)         (0---)    
     4s     x8  12                   |                                                              (3---)         (2---)         (1---)         (0---)
     4s     x16 12                   |  (3---)         (2---)         (1---)         (0---)
     8s     x1   8                   |                                                                                                                                                                   (3210----)    
     8s     x2   8                   |                                                                                                                                                    (31------)     (20------)    
     8s     x4   8                   |                                                                                                                      (3-------)     (2-------)     (1-------)     (0-------)    
     8s     x8   8                   |                                                          (3-------)     (2-------)     (1-------)     (0-------) 
*/
function automatic [NBIT-1:0] BuildEDSData;
  input  [5:0]      active_nb;           // Active Number of Symbols. bit0=1s, bit1=2s, bit2=4s, bit3=8s
  input  [4:0]      active_lane_cnt;     // Active Number of Lanes
  input  [3:0]      sym_cnt;             // Current Block Symbol Count
  input  [3:0]      lane_num;            // Current Lane Number

begin
    BuildEDSData = {NBIT{1'b0}};

    if (lane_num == 0) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             // x1 Active Link
             14'b00010_00001_1100 : BuildEDSData[15:0]  = {`EPX16_EDS_TOKEN_1, `EPX16_EDS_TOKEN_0};
             14'b00010_00001_1110 : BuildEDSData[15:0]  = {`EPX16_EDS_TOKEN_3, `EPX16_EDS_TOKEN_2};
             // x2 Active Link
             14'b00010_00010_1110 : BuildEDSData[15:0]  = {`EPX16_EDS_TOKEN_2, `EPX16_EDS_TOKEN_0};
             // x4 Active Link
             14'b00010_00100_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_0;
             // 4s
             // x1 Active Link
             14'b00100_00001_1100 : BuildEDSData[31:0]  = {`EPX16_EDS_TOKEN_3, `EPX16_EDS_TOKEN_2, `EPX16_EDS_TOKEN_1, `EPX16_EDS_TOKEN_0};
             // x2 Active Link
             14'b00100_00010_1100 : BuildEDSData[31:16] = {`EPX16_EDS_TOKEN_2, `EPX16_EDS_TOKEN_0};
             // x4 Active Link
             14'b00100_00100_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_0;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
    end //if (lane_num == 0)

    if (lane_num == 1) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             // x1 Active Link
             14'b00010_00010_1110 : BuildEDSData[15:0]  = {`EPX16_EDS_TOKEN_3, `EPX16_EDS_TOKEN_1};
             // x4 Active Link
             14'b00010_00100_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_1;
             // 4s
             14'b00100_00010_1100 : BuildEDSData[31:16] = {`EPX16_EDS_TOKEN_3, `EPX16_EDS_TOKEN_1};
             // x4 Active Link
             14'b00100_00100_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_1;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
    end //if (lane_num == 1)

     if (lane_num == 2) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_00100_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_2;
             // 4s
             14'b00100_00100_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_2;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 2)

     if (lane_num == 3) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_00100_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_3;
             // 4s
             14'b00100_00100_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_3;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end // if (lane_num == 3)

     if (lane_num == 4) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_01000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_0;
             // 4s
             14'b00100_01000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_0;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 4)

     if (lane_num == 5) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_01000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_1;
             // 4s
             14'b00100_01000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_1;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 5)

     if (lane_num == 6) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_01000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_2;
             // 4s
             14'b00100_01000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_2;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 6)

     if (lane_num == 7) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_01000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_3;
             // 4s
             14'b00100_01000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_3;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end // if (lane_num == 7)

     if (lane_num == 12) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_10000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_0;
             // 4s
             14'b00100_10000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_0;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 13)

     if (lane_num == 13) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_10000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_1;
             // 4s
             14'b00100_10000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_1;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 13)

     if (lane_num == 14) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_10000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_2;
             // 4s
             14'b00100_10000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_2;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 14)

     if (lane_num == 15) begin
         case ({active_nb[4:0], active_lane_cnt, sym_cnt})
             // 2s
             14'b00010_10000_1110 : BuildEDSData[15:8]  = `EPX16_EDS_TOKEN_3;
             // 4s
             14'b00100_10000_1100 : BuildEDSData[31:24] = `EPX16_EDS_TOKEN_3;
                            default  : BuildEDSData        = {NBIT{1'b0}};
         endcase
     end //if (lane_num == 15)
end
endfunction // BuildEDSData

// Function to indicate when last byte of EDS Token has been inserted in Data Block
function automatic  EndOfEDS;
  input  [5:0]      active_nb;           // Active Number of Symbols. bit0=1s, bit1=2s, bit2=4s
  input  [3:0]      sym_cnt;             // Current Block Symbol Count

begin
    EndOfEDS = 1'b0;

         case ({active_nb[4:0], sym_cnt })
             // 2s
             // x1 Active Link
             9'b00010_1110 : EndOfEDS   = 1'b1;
             // 4s
             // x1 Active Link
             9'b00100_1100 : EndOfEDS   = 1'b1;
                       default : EndOfEDS   = 1'b0;
         endcase
end
endfunction // EndOfEDS

// Function to build data for margin command and status within Control SKP Ordered Set for 16 GT/s.
// Downstream/Upstream Port
//   Symbol13 [7] Data Parity [6] First Retimer Data Parity [5] Second Retimer Data Parity [4:0] Margin CRC
//   Symbol14 [7] Margin Parity [6:0] {Usage Model, Margin Type[2:0],Receiver Number[2:0]}
//   Symbol15 [7:0] Margin Payload[7:0]
function automatic [23:0] BuildSKPMarginSym;
  input             skp_parity;        // Even Parity of prior Data Blocks since the last SKP Ordered
  input  [15:0]     margin_info;       // [15:8]Margin Payload,[7]Rsvd,[6]Usage Molde,[5:3]Margin Type,[2:0] Receiver Number
  reg    [14:0]     d;
  reg    [4:0]      margin_crc;
  reg               margin_parity;
begin
  d = {margin_info[15:8],margin_info[6:0]};
  margin_crc[0] = d[0] ^ d[3] ^ d[5] ^ d[6] ^ d[9]  ^ d[10] ^ d[11] ^ d[12] ^ d[13];
  margin_crc[1] = d[1] ^ d[4] ^ d[6] ^ d[7] ^ d[10] ^ d[11] ^ d[12] ^ d[13] ^ d[14];
  margin_crc[2] = d[0] ^ d[2] ^ d[3] ^ d[6] ^ d[7]  ^ d[8]  ^ d[9]  ^ d[10] ^ d[14];
  margin_crc[3] = d[1] ^ d[3] ^ d[4] ^ d[7] ^ d[8]  ^ d[9]  ^ d[10] ^ d[11];
  margin_crc[4] = d[2] ^ d[4] ^ d[5] ^ d[8] ^ d[9]  ^ d[10] ^ d[11] ^ d[12];
  margin_parity = (^margin_crc) ^ (^d);
  BuildSKPMarginSym = {margin_info[15:8],                            // Symbol15
                       margin_parity,margin_info[6:0],               // Symbol14
                       skp_parity,skp_parity,skp_parity,margin_crc}; // Symbol13
end
endfunction // BuildSKPMarginSym

// Function to build data for SKP Ordered Set for 8 GT/s and above Data Rates.
// Symbol 0 to Symbol 12 of a SKP OS are always constant predefined values.
// Symbol 13 to Symbol 15 vary according to the current state of the LTSSM.
function automatic [PAT_NBIT-1:0] BuildSKPData;
  input  [PAT_NBIT-1:0] pat_data;        // SKP Pattern Data
  input             parity;              // Data Parity/~LFSR[22]
  input  [22:0]     lfsr;                // LFSR Value
  input  [7:0]      err_cnt;             // Error Count
  input  [5:0]      active_nb;           // Active Number of Symbols. bit0=1s, bit1=2s, bit2=4s, bit3=8s, bit4=16s
  input  [3:0]      sym_cnt;             // Current Block Symbol Count
  input             ltssm_in_compliance; // Indicates LTSSM is in Polling.Compliance
  input             in_ctlskip;          // Indicates now data is in the enhanced SKIP-OS
  input  [23:0]     skp_margin_sym;      // Symbol#13-15 for Enahnced SKP
begin
    BuildSKPData = pat_data;
    if(in_ctlskip) begin // Control SKP-OS
         case ({active_nb[4:0], sym_cnt })
             // 2s
              9'b00010_1100 : BuildSKPData[15:0]   = {skp_margin_sym[ 7: 0],  // Symbol 13
                                                         pat_data[7:0]};      // Symbol 12
              9'b00010_1110 : BuildSKPData[15:0]   = {skp_margin_sym[23:16],  // Symbol 15
                                                      skp_margin_sym[15: 8]}; // Symbol 14
             // 4s
              9'b00100_1100 : BuildSKPData[31:8]  = {skp_margin_sym[23: 0]}; // Symbol 15,14,13
             // For the default case, the original SKP pattern from the
             // pattern generator is maintained and no symbols are substituted.
             // The original SKP pattern  consists of AAh for Symbol 0 to Symbol 11,
             // E1h for Symbol 12 and AAh for Symbol 13 to Symbol 15.
             default : BuildSKPData   = pat_data;
         endcase
    end else 
    begin
         case ({active_nb[4:0], sym_cnt, ltssm_in_compliance })
             // 2s
             // LTSSM is not in Polling.Compliance
             10'b00010_1100_0 : BuildSKPData[15:0]   = { parity, lfsr[22:16], // Symbol 13
                                                         pat_data[7:0]};      // Symbol 12
             10'b00010_1110_0 : BuildSKPData[15:0]   = { lfsr[7:0],           // Symbol 15
                                                         lfsr[15:8]};         // Symbol 14
             // LTSSM is in Polling.Compliance
             10'b00010_1100_1 : BuildSKPData[15:0]   = { 8'hAA,               // Symbol 13
                                                         pat_data[7:0]};      // Symbol 12
             10'b00010_1110_1 : BuildSKPData[15:0]   = { ~err_cnt,            // Symbol 15
                                                          err_cnt};           // Symbol 14
             // 4s
             // LTSSM is not in Polling.Compliance
             10'b00100_1100_0 : BuildSKPData[31:0]   = { lfsr[7:0],           // Symbol 15
                                                         lfsr[15:8],          // Symbol 14
                                                         parity, lfsr[22:16], // Symbol 13
                                                         pat_data[7:0]};      // Symbol 12
             // LTSSM is in Polling.Compliance
             10'b00100_1100_1 : BuildSKPData[31:0]   = { ~err_cnt,            // Symbol 15
                                                         err_cnt,             // Symbol 14
                                                         8'hAA,               // Symbol 13
                                                         pat_data[7:0]};      // Symbol 12
             // For the default case, the original SKP pattern from the
             // pattern generator is maintained and no symbols are substituted.
             // The original SKP pattern  consists of AAh for Symbol 0 to Symbol 11,
             // E1h for Symbol 12 and AAh for Symbol 13 to Symbol 15.
             default : BuildSKPData   = pat_data;

         endcase
    end
end
endfunction // BuildSKPData

// Function to build data for null flit implied eds
// Symbol 0 is 4b on lane 0 and Lane 1
function automatic [NBIT-1:0] BuildiEDSData;
  input  [5:0]      link_mode;           // linkwidth
  input  [5:0]      active_nb;           // Active Number of Symbols. bit0=1s, bit1=2s, bit2=4s, bit3=8s, bit4=16s
  input  [3:0]      sym_cnt;             // Current Block Symbol Count
  input  [3:0]      lane_num;            // lane#
begin
    BuildiEDSData = {NBIT{1'b0}};
         case ({active_nb[4:0], sym_cnt, lane_num})
             // 4s
              13'b00100_0000_0000 : BuildiEDSData[15:0] = (link_mode == 1) ? 16'h4B4B : 16'h004B; // Symbol 0 on lane 0 = h4B. if only one lane active, 16'h4B4B
              13'b00100_0000_0001 : BuildiEDSData[7:0]  = (link_mode == 1) ? 8'h00    : 8'h4B;    // Symbol 0 on Lane 1 = h4B
              default            : BuildiEDSData       = {NBIT{1'b0}};
         endcase
end
endfunction // BuildiEDSData

// Function to build data for Compliance Pattern Block 1 for 8 GT/s and above Data Rates.
// Block 1 is Lane-based data
function automatic [NBIT-1:0] BuildG3CmpData;
  input  [NBIT-1:0] pat_data;            // G3Cmp Pattern Data
  input  [2:0]      lane_num;            // Lane number % 8
  input  [3:0]      tx_preset;           // Tx preset being used
  input  [5:0]      active_nb;           // Active Number of Symbols. bit0=1s, bit1=2s, bit2=4s, bit3 = 8s, bit4=16s
  input  [3:0]      sym_cnt;             // Current Block Symbol Count
  input  [1:0]      cmpl_count;          // Indicates this is Block 1
  input             jmp_en;              // Enable of Jitter Measurment Pattern
begin
    BuildG3CmpData = pat_data;
    if (jmp_en == 1) begin
            case (active_nb[4:0])
                // 2s
                5'b00010 : BuildG3CmpData[15:0]   = {2{8'h55}}; // Symbol 0-15 Lane 0-15
                // 4s
                5'b00100 : BuildG3CmpData[31:0]   = {4{8'h55}}; // Symbol 0-15 Lane 0-15
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
    end else 
    if ( cmpl_count == 1 ) begin
        if (lane_num == 0) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h55}; // Symbol 6-7 Lane 0
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 8-9 Lane 0
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 10-11 Lane 0
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 12-13 Lane 0
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 14-15 Lane 0
                // 4s
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h55,8'h55,8'h55}; // Symbol 4-7 Lane 0
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h00}; // Symbol 12-15 Lane 0
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h00}; // Symbol 12-15 Lane 0
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 0)

        if (lane_num == 1) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 0-1 Lane 1
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 2-3 Lane 1
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 4-5 Lane 1
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 1
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h55,8'h1e}; // Symbol 8-9 Lane 1
                // 4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'hff,8'hff}; // Symbol 0-3 Lane 1
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 1
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h1e}; // Symbol 8-11 Lane 1
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 1)

        if (lane_num == 2) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 0-1 Lane 2
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 2-3 Lane 2
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'hc0,8'hff}; // Symbol 4-5 Lane 2
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 2
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h2d}; // Symbol 8-9 Lane 2
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 10-11 Lane 2
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 2
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 2
                // 4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'hff}; // Symbol 0-3 Lane 2
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'hc0,8'hff}; // Symbol 4-7 Lane 2
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h2d}; // Symbol 8-11 Lane 2
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 2
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 2)

        if (lane_num == 3) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 0-1 Lane 3
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 2-3 Lane 3
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'h00,8'hc0}; // Symbol 4-5 Lane 3
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 3
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h3c}; // Symbol 8-9 Lane 3
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 10-11 Lane 3
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 3
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 3
                // 4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'hff}; // Symbol 0-3 Lane 3
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'hc0}; // Symbol 4-7 Lane 3
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h3c}; // Symbol 8-11 Lane 3
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 3
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 3)

        if (lane_num == 4) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h55}; // Symbol 6-7 Lane 4
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h4b}; // Symbol 8-9 Lane 4
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 10-11 Lane 4
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 12-13 Lane 4
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'h7f}; // Symbol 14-15 Lane 4
                // 4s
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h55,8'h55,8'h55}; // Symbol 4-7 Lane 4
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h4b}; // Symbol 8-11 Lane 4
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'h7f,8'h00,8'h00}; // Symbol 12-15 Lane 4
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 4)

        if (lane_num == 5) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 0-1 Lane 5
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 2-3 Lane 5
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'he0,8'hff}; // Symbol 4-5 Lane 5
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 5
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h55,8'h5a}; // Symbol 8-9 Lane 5
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'hff}; // Symbol 0-3 Lane 5
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'he0,8'hff}; // Symbol 4-7 Lane 5
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h5a}; // Symbol 8-11 Lane 5
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 5)

        if (lane_num == 6) begin
            case ({active_nb[4:0], sym_cnt})
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 0-1 Lane 6
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 2-3 Lane 6
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 4-5 Lane 6
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 6
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h69}; // Symbol 8-9 Lane 6
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 10-11 Lane 6
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h07}; // Symbol 12-13 Lane 6
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 6
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 0-3 Lane 6
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 6
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h69}; // Symbol 8-11 Lane 6
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h07}; // Symbol 12-15 Lane 6
               // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 6)

        if (lane_num == 7) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 0-1 Lane 7
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 2-3 Lane 7
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 4-5 Lane 7
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 7
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'hf0,8'h78}; // Symbol 8-9 Lane 7
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 10-11 Lane 7
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 12-13 Lane 7
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'h00,8'h00}; // Symbol 14-15 Lane 7
                // 4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 0-3 Lane 7
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 7
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'hf0,8'h78}; // Symbol 12-15 Lane 7
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h00}; // Symbol 12-15 Lane 7
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 7)

    end else if ( cmpl_count == 2 ) begin //if ( cmpl_count == 1
        if (lane_num == 0) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 1-0 Lane 0
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 3-2 Lane 0
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 0
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 12-13 Lane 0
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 0
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 3-0 Lane 0
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 0
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'hff}; // Symbol 12-15 Lane 0
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 0)

        if (lane_num == 1) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 1-0 Lane 1
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 3-2 Lane 1
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 1
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h1e}; // Symbol 8-9 Lane 1
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 1
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 1
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 3-0 Lane 1
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 1
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h1e}; // Symbol 8-11 Lane 1
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 1
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 1)

        if (lane_num == 2) begin
            case ({active_nb[4:0], sym_cnt})
                // 2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 1-0 Lane 2
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 3-2 Lane 2
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 5-4 Lane 2
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h55}; // Symbol 6-7 Lane 2
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h2d}; // Symbol 8-9 Lane 2
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 2
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 2
                // 4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h55}; // Symbol 3-0 Lane 2
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h55,8'h55,8'h55}; // Symbol 4-7 Lane 2
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h2d}; // Symbol 8-11 Lane 2
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 2
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 2)

        if (lane_num == 3) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 1-0 Lane 3
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 3-2 Lane 3
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 3
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h55,8'h3c}; // Symbol 8-9 Lane 3
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 10-11 Lane 3
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 12-13 Lane 3
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 14-15 Lane 3
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 3-0 Lane 3
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 3
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h3c}; // Symbol 8-11 Lane 3
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h55}; // Symbol 12-15 Lane 3
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 3)

        if (lane_num == 4) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 1-0 Lane 4
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 3-2 Lane 4
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 4
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h4b}; // Symbol 8-9 Lane 4
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 4
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 4
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 3-0 Lane 4
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 4
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h4b}; // Symbol 8-11 Lane 4
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 4
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 4)

        if (lane_num == 5) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 1-0 Lane 5
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 3-2 Lane 5
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 5
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h5a}; // Symbol 8-9 Lane 5
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 5
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 5
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 3-0 Lane 5
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 5
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h5a}; // Symbol 8-11 Lane 5
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 5

                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 5)

        if (lane_num == 6) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 1-0 Lane 6
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 3-2 Lane 6
                9'b00010_0100 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 5-4 Lane 6
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h55}; // Symbol 6-7 Lane 6
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h00,8'h69}; // Symbol 8-9 Lane 6
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'hff,8'h0f}; // Symbol 12-13 Lane 6
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 14-15 Lane 6
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h55}; // Symbol 3-0 Lane 6
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h55,8'h55,8'h55}; // Symbol 4-7 Lane 6
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h00,8'h00,8'h00,8'h69}; // Symbol 8-11 Lane 6
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'hff,8'hff,8'hff,8'h0f}; // Symbol 12-15 Lane 6
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 6)

        if (lane_num == 7) begin
            case ({active_nb[4:0], sym_cnt})
                //2s
                9'b00010_0000 : BuildG3CmpData[15:0]   = {8'hff,8'hff}; // Symbol 1-0 Lane 7
                9'b00010_0010 : BuildG3CmpData[15:0]   = {8'hf0,8'hff}; // Symbol 3-2 Lane 7
                9'b00010_0110 : BuildG3CmpData[15:0]   = {{tx_preset,~tx_preset},8'h00}; // Symbol 6-7 Lane 7
                9'b00010_1000 : BuildG3CmpData[15:0]   = {8'h55,8'h78}; // Symbol 8-9 Lane 7
                9'b00010_1010 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 10-11 Lane 7
                9'b00010_1100 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 12-13 Lane 7
                9'b00010_1110 : BuildG3CmpData[15:0]   = {8'h55,8'h55}; // Symbol 14-15 Lane 7
                //4s
                9'b00100_0000 : BuildG3CmpData[31:0]   = {8'hf0,8'hff,8'hff,8'hff}; // Symbol 3-0 Lane 7
                9'b00100_0100 : BuildG3CmpData[31:0]   = {{tx_preset,~tx_preset},8'h00,8'h00,8'h00}; // Symbol 4-7 Lane 7
                9'b00100_1000 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h78}; // Symbol 8-11 Lane 7
                9'b00100_1100 : BuildG3CmpData[31:0]   = {8'h55,8'h55,8'h55,8'h55}; // Symbol 12-15 Lane 7
                // For the default case, the original G3Cmp pattern from the
                // pattern generator is maintained and no symbols are substituted.
                default : BuildG3CmpData   = pat_data;
            endcase
        end // if (lane_num == 7)
    end else //end else if ( cmpl_count == 2
            BuildG3CmpData   = pat_data;
end
endfunction // BuildG3CmpData


// Function returns the bit number of the hot bit in a one hot.
// used for data cycles calculation on int_link_mode
function automatic [3:0]   OneHotToBitNum;
    input   [4:0]   onehot;

begin
    OneHotToBitNum = 0;
    case (onehot)
        5'b00001 :  OneHotToBitNum = 0;
        5'b00010 :  OneHotToBitNum = 1;
        5'b00100 :  OneHotToBitNum = 2;
        5'b01000 :  OneHotToBitNum = 3;
        5'b10000 :  OneHotToBitNum = 4;
        default  :  OneHotToBitNum = 0;
    endcase
end
endfunction

// Function to grab a byte from a bus
function automatic [7:0] Bget;
input [DW-1:0]   vector;
input [SLICE_WD-1:0]      index_i;

reg   [LOG2NBYTE-1:0]      index;
begin
    index = index_i[LOG2NBYTE-1:0]; // to avoid spyglass warning
    Bget = vector[index*8 +: 8];
end
endfunction

function automatic [SLICE_WD-1:0] Calc_pkt_end;
  input  [NW-1:0]   eot_dw;                     // one hot indicating the last Dword
  input  [SLICE_WD:0] data_cycles;                // the number of cycles needed to send one CX_DW of data

  reg    [SLICE_WD:0] pkt_cycles;                 // the number of cycles needed to send this chunk of data
begin
// Don't care about cases where eot_dw[DW-1] is set or when data_cycles == 1
// because then early_pkt_end is never asserted therefore output of
// Calc_pkt_end is not used.

/*
PL_NW_8 implementation:

                          data_cycles
                       2  4   8   16   32

           1000_0000   2  4   8   16   32  -> data_cycles
           0100_0000   2  4   7   14   28  -> data_cycles - data_cycles/8
           0010_0000   2  3   6   12   24  -> data_cycles - data_cycles/4
 eot_dw    0001_0000   2  3   5   10   20  -> data_cycles - data_cycles/4 - data_cycles/8
           0000_1000   1  2   4   8    16  -> data_cycles/2
           0000_0100   1  2   3   6    12  -> data_cycles/2 - data_cycles/8
           0000_0010   0  1   2   4    8   -> data_cycles/4
           0000_0001   0  0   1   2    4   -> data_cycles/8


PL_NW_16 implementation obtained scaling the above, no attempt is made for more compact coding style:

                                    data_cycles
                                 2  4   8   16   32   64

           1000_0000_0000_0000   2  4   8   16   32   64 -> data_cycles
           0100_0000_0000_0000   2  4   8   15   30   60 -> data_cycles - data_cycles/16
           0010_0000_0000_0000   2  4   7   14   28   56 -> data_cycles - data_cycles/8
 eot_dw    0001_0000_0000_0000   2  4   7   13   26   52 -> data_cycles - data_cycles/8 - data_cycles/16

           0000_1000_0000_0000   2  3   6   12   24   48 -> data_cycles - data_cycles/4
           0000_0100_0000_0000   2  3   6   11   22   44 -> data_cycles - data_cycles/4 - data_cycles/16 
           0000_0010_0000_0000   2  3   5   10   20   40 -> data_cycles - data_cycles/4 - data_cycles/8
           0000_0001_0000_0000   2  3   5    9   18   36 -> data_cycles - data_cycles/4 - data_cycles/8 - data_cycles/16

           0000_0000_1000_0000   1  2   4    8   16   32 -> data_cycles/2 
           0000_0000_0100_0000   1  2   4    7   14   28 -> data_cycles/2 - data_cycles/16
           0000_0000_0010_0000   1  2   3    6   12   24 -> data_cycles/2 - data_cycles/8
           0000_0000_0001_0000   1  2   3    5   10   20 -> data_cycles/2 - data_cycles/8 - data_cycles/16
           0000_0000_0000_1000   0  1   2    4    8   16 -> data_cycles/4 
           0000_0000_0000_0100   0  1   2    3    6   12 -> data_cycles/4 - data_cycles/16 
           0000_0000_0000_0010   0  0   1    2    4    8 -> data_cycles/8
           0000_0000_0000_0001   0  0   0    1    2    4 -> data_cycles/16

*/
    case (1'b1)
        eot_dw[15] : pkt_cycles = data_cycles;
        eot_dw[14] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/16
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 4; // data_cycles == 4
                5'b00010 : pkt_cycles = 8; // data_cycles == 8
                5'b00100 : pkt_cycles = 15; // data_cycles == 16
                5'b01000 : pkt_cycles = 30; // data_cycles == 32
                5'b10000 : pkt_cycles = 60; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[13] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/8
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 4; // data_cycles == 4
                5'b00010 : pkt_cycles = 7; // data_cycles == 8
                5'b00100 : pkt_cycles = 14; // data_cycles == 16
                5'b01000 : pkt_cycles = 28; // data_cycles == 32
                5'b10000 : pkt_cycles = 56; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[12] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/8 - data_cycles/16
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 4; // data_cycles == 4
                5'b00010 : pkt_cycles = 7; // data_cycles == 8
                5'b00100 : pkt_cycles = 13; // data_cycles == 16
                5'b01000 : pkt_cycles = 26; // data_cycles == 32
                5'b10000 : pkt_cycles = 52; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[11] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/4
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 3; // data_cycles == 4
                5'b00010 : pkt_cycles = 6; // data_cycles == 8
                5'b00100 : pkt_cycles = 12; // data_cycles == 16
                5'b01000 : pkt_cycles = 24; // data_cycles == 32
                5'b10000 : pkt_cycles = 48; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[10] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/4 - data_cycles/16 
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 3; // data_cycles == 4
                5'b00010 : pkt_cycles = 6; // data_cycles == 8
                5'b00100 : pkt_cycles = 11; // data_cycles == 16
                5'b01000 : pkt_cycles = 22; // data_cycles == 32
                5'b10000 : pkt_cycles = 44; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[9] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/4 - data_cycles/8 - data_cycles/16
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 3; // data_cycles == 4
                5'b00010 : pkt_cycles = 5; // data_cycles == 8
                5'b00100 : pkt_cycles = 10; // data_cycles == 16
                5'b01000 : pkt_cycles = 20; // data_cycles == 32
                5'b10000 : pkt_cycles = 40; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[8] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles - data_cycles/4 - data_cycles/8 - data_cycles/16 - data_cycles/32
                5'b00000 : pkt_cycles = 2; // data_cycles == 2
                5'b00001 : pkt_cycles = 3; // data_cycles == 4
                5'b00010 : pkt_cycles = 5; // data_cycles == 8
                5'b00100 : pkt_cycles = 9; // data_cycles == 16
                5'b01000 : pkt_cycles = 18; // data_cycles == 32
                5'b10000 : pkt_cycles = 36; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[7]: //pkt_cycles = data_cycles/2
            pkt_cycles = data_cycles >> 1;
        eot_dw[6] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles/2 - data_cycles/16
                5'b00000 : pkt_cycles = 1; // data_cycles == 2
                5'b00001 : pkt_cycles = 2; // data_cycles == 4
                5'b00010 : pkt_cycles = 4; // data_cycles == 8
                5'b00100 : pkt_cycles = 7; // data_cycles == 16
                5'b01000 : pkt_cycles = 14; // data_cycles == 32
                5'b10000 : pkt_cycles = 28; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[5] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles/2 - data_cycles/8
                5'b00000 : pkt_cycles = 1; // data_cycles == 2
                5'b00001 : pkt_cycles = 2; // data_cycles == 4
                5'b00010 : pkt_cycles = 3; // data_cycles == 8
                5'b00100 : pkt_cycles = 6; // data_cycles == 16
                5'b01000 : pkt_cycles = 12; // data_cycles == 32
                5'b10000 : pkt_cycles = 24; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[4] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles/2 - data_cycles/8 - data_cycles/16
                5'b00000 : pkt_cycles = 1; // data_cycles == 2
                5'b00001 : pkt_cycles = 2; // data_cycles == 4
                5'b00010 : pkt_cycles = 3; // data_cycles == 8
                5'b00100 : pkt_cycles = 5; // data_cycles == 16
                5'b01000 : pkt_cycles = 10; // data_cycles == 32
                5'b10000 : pkt_cycles = 20; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[3]: //pkt_cycles = data_cycles/4
            pkt_cycles = data_cycles >> 2;
        eot_dw[2] : 
            case(data_cycles[6:2]) //pkt_cycles = data_cycles/4 - data_cycles/16 
                5'b00000 : pkt_cycles = 0; // data_cycles == 2
                5'b00001 : pkt_cycles = 1; // data_cycles == 4
                5'b00010 : pkt_cycles = 2; // data_cycles == 8
                5'b00100 : pkt_cycles = 3; // data_cycles == 16
                5'b01000 : pkt_cycles = 6; // data_cycles == 32
                5'b10000 : pkt_cycles = 12; // data_cycles == 64
                default: pkt_cycles = 0;
            endcase
        eot_dw[1]: //pkt_cycles = data_cycles/8
            pkt_cycles = data_cycles >> 3;
        eot_dw[0]: //pkt_cycles = data_cycles/16
            pkt_cycles = data_cycles >> 4;
        default  : pkt_cycles = data_cycles;
    endcase
  Calc_pkt_end = (pkt_cycles == 6'h0) ? 5'h0 : pkt_cycles - 1;
end
endfunction

assign xmlh_rst_flit_alignment = 1'b0;
assign skp_win                 = 1'b0;
assign eie_win                 = 1'b0;
assign eid_win                 = 1'b0;
assign pid_rcvd_pulse          = 1'b0;
assign eie_win_i               = 1'b0;
assign eid_win_i               = 1'b0;
wire   eie_eid_0               = 1'b0;
always @* begin : eie_eid_win_d_PROC
    skp_win_d                  = 1'b0;
    eie_win_d                  = 1'b0;
    eid_win_d                  = 1'b0;
    skp_win_d                  = eie_eid_0;
    eie_win_d                  = eie_eid_0;
    eid_win_d                  = eie_eid_0;
end // eie_eid_win_d_PROC

wire [11:0] pid_count_0s;
assign      pid_count_0s = 0;
always @* begin : pid_count_0s_PROC
    pid_count = 0;
    pid_count = pid_count_0s;
end // pid_count_0s_PROC

// assertions
// check that each command from the ltssm is executed before we start processing the next command
// active lanes and active_nb should never change when data is being processed
//  chunk_cnt should never be greater than data_cycles;

// xdlh_xmlh_pad[0] should never be set.

`ifndef SYNTHESIS
wire    [(24*8)-1:0]        XBYTE_CURNT_STATE;
wire    [(24*8)-1:0]        XBYTE_NEXT_STATE;
wire    [(24*8)-1:0]        NEXT_CMD;
wire    [(21*8)-1:0]        LTSSM_CMD;
wire    [(21*8)-1:0]        NEXT_PAT;
wire    [5*8-1:0]           XMT_LAYER2_STATE;
wire    [5*8-1:0]           NEXT_XMT_LAYER2_STATE;

assign  XBYTE_CURNT_STATE =
               ( xbyte_curnt_state == S_IDLE                   ) ? "S_IDLE" :
               ( xbyte_curnt_state == S_EIDLE                  ) ? "S_EIDLE" :
               ( xbyte_curnt_state == S_XMT_EIDLE              ) ? "S_XMT_EIDLE" :
               ( xbyte_curnt_state == S_FTS                    ) ? "S_FTS" :
               ( xbyte_curnt_state == S_TS                     ) ? "S_TS" :
               ( xbyte_curnt_state == S_SKIP                   ) ? "S_SKIP" :
               ( xbyte_curnt_state == S_CMP                    ) ? "S_CMP" :
               ( xbyte_curnt_state == S_XPKT_WAIT4_START       ) ? "S_XPKT_WAIT4_START" :
               ( xbyte_curnt_state == S_XPKT_WAIT4_STOP        ) ? "S_XPKT_WAIT4_STOP" :
               ( xbyte_curnt_state == S_EIES                   ) ? "S_EIES" :
               ( xbyte_curnt_state == S_EIES_SYM               ) ? "S_EIES_SYM" :
               ( xbyte_curnt_state == S_SDS                     ) ? "S_SDS" :
               ( xbyte_curnt_state == S_EDS                     ) ? "S_EDS" :
                                                                   "Bogus";

assign  XBYTE_NEXT_STATE =
               ( xbyte_next_state == S_IDLE                   ) ? "S_IDLE" :
               ( xbyte_next_state == S_EIDLE                  ) ? "S_EIDLE" :
               ( xbyte_next_state == S_XMT_EIDLE              ) ? "S_XMT_EIDLE" :
               ( xbyte_next_state == S_FTS                    ) ? "S_FTS" :
               ( xbyte_next_state == S_TS                     ) ? "S_TS" :
               ( xbyte_next_state == S_SKIP                   ) ? "S_SKIP" :
               ( xbyte_next_state == S_CMP                    ) ? "S_CMP" :
               ( xbyte_next_state == S_XPKT_WAIT4_START       ) ? "S_XPKT_WAIT4_START" :
               ( xbyte_next_state == S_XPKT_WAIT4_STOP        ) ? "S_XPKT_WAIT4_STOP" :
               ( xbyte_next_state == S_EIES                   ) ? "S_EIES" :
               ( xbyte_next_state == S_EIES_SYM               ) ? "S_EIES_SYM" :
               ( xbyte_next_state == S_SDS                    ) ? "S_SDS" :
               ( xbyte_next_state == S_EDS                    ) ? "S_EDS" :
                                                                   "Bogus";

assign  NEXT_CMD =
               ( next_cmd == S_IDLE                   ) ? "S_IDLE" :
               ( next_cmd == S_EIDLE                  ) ? "S_EIDLE" :
               ( next_cmd == S_XMT_EIDLE              ) ? "S_XMT_EIDLE" :
               ( next_cmd == S_FTS                    ) ? "S_FTS" :
               ( next_cmd == S_TS                     ) ? "S_TS" :
               ( next_cmd == S_SKIP                   ) ? "S_SKIP" :
               ( next_cmd == S_CMP                    ) ? "S_CMP" :
               ( next_cmd == S_XPKT_WAIT4_START       ) ? "S_XPKT_WAIT4_START" :
               ( next_cmd == S_XPKT_WAIT4_STOP        ) ? "S_XPKT_WAIT4_STOP" :
               ( next_cmd == S_EIES                   ) ? "S_EIES" :
               ( next_cmd == S_EIES_SYM               ) ? "S_EIES_SYM" :
               ( next_cmd == S_SDS                    ) ? "S_SDS" :
                                                                   "Bogus";
assign  LTSSM_CMD =
               ( ltssm_cmd == `EPX16_SEND_IDLE             ) ? "SEND_IDLE" :
               ( ltssm_cmd == `EPX16_SEND_EIDLE            ) ? "SEND_EIDLE" :
               ( ltssm_cmd == `EPX16_XMT_IN_EIDLE          ) ? "XMT_IN_EIDLE" :
               ( ltssm_cmd == `EPX16_SEND_RCVR_DETECT_SEQ  ) ? "SEND_RCVR_DETECT_SEQ" :
               ( ltssm_cmd == `EPX16_SEND_TS1              ) ? "SEND_TS1" :
               ( ltssm_cmd == `EPX16_SEND_TS2              ) ? "SEND_TS2" :
               ( ltssm_cmd == `EPX16_COMPLIANCE_PATTERN    ) ? "COMPLIANCE_PATTERN" :
               ( ltssm_cmd == `EPX16_MOD_COMPL_PATTERN     ) ? "MOD_COMPL_PATTERN" :
               ( ltssm_cmd == `EPX16_SEND_BEACON           ) ? "SEND_BEACON" :
               ( ltssm_cmd == `EPX16_SEND_N_FTS            ) ? "SEND_N_FTS" :
               ( ltssm_cmd == `EPX16_NORM                  ) ? "NORM" :
                                                         "Bogus";
assign  NEXT_PAT =
               ( next_pat == `EPX16_SEND_IDLE             ) ? "SEND_IDLE" :
               ( next_pat == `EPX16_SEND_EIDLE            ) ? "SEND_EIDLE" :
               ( next_pat == `EPX16_XMT_IN_EIDLE          ) ? "XMT_IN_EIDLE" :
               ( next_pat == `EPX16_SEND_RCVR_DETECT_SEQ  ) ? "SEND_RCVR_DETECT_SEQ" :
               ( next_pat == `EPX16_SEND_TS1              ) ? "SEND_TS1" :
               ( next_pat == `EPX16_SEND_TS2              ) ? "SEND_TS2" :
               ( next_pat == `EPX16_COMPLIANCE_PATTERN    ) ? "COMPLIANCE_PATTERN" :
               ( next_pat == `EPX16_MOD_COMPL_PATTERN     ) ? "MOD_COMPL_PATTERN" :
               ( next_pat == `EPX16_SEND_BEACON           ) ? "SEND_BEACON" :
               ( next_pat == `EPX16_SEND_N_FTS            ) ? "SEND_N_FTS" :
               ( next_pat == `EPX16_NORM                  ) ? "NORM" :
               ( next_pat == `EPX16_SEND_SKP              ) ? "SEND_SKP" :
               ( next_pat == `EPX16_SEND_EIES             ) ? "SEND_EIES" :
               ( next_pat == `EPX16_SEND_EIES_SYM         ) ? "SEND_EIES_SYM" :
               ( next_pat == `EPX16_SEND_SDS              ) ? "SEND_SDS" :
                                                         "Bogus";


//VCS coverage off
wire ltssm_rcvry_lock            = smlh_ltssm_state == `EPX16_S_RCVRY_LOCK;
wire pkt_pending_assert_window_m = ltssm_rcvry_lock & (ltssm_cmd_i == `EPX16_NORM);
wire pkt_pending_assert_window_0 = ltssm_rcvry_lock & ltssm_cmd_is_norm[0] & ~(ltssm_cmd_i == `EPX16_NORM);
wire pkt_pending_assert_window_1 = ltssm_rcvry_lock & ltssm_cmd_is_norm[1] & ~ltssm_cmd_is_norm[0];
wire pkt_pending_assert_window_2 = ltssm_rcvry_lock & ltssm_cmd_is_norm[2] & ~ltssm_cmd_is_norm[1];
reg  xdlh_xmlh_pkt_pending_d;
wire xdlh_xmlh_pkt_pending_rising_edge;
wire pkt_pending_detected_within_window_m, pkt_pending_detected_within_window_0;
wire pkt_pending_detected_within_window_1, pkt_pending_detected_within_window_2;
always @(posedge core_clk or negedge core_rst_n) begin : xdlh_xmlh_pkt_pending_d_PROC
    if ( ~core_rst_n )
        xdlh_xmlh_pkt_pending_d <= #TP 0;
    else
        xdlh_xmlh_pkt_pending_d <= #TP xdlh_xmlh_pkt_pending;
end // xdlh_xmlh_pkt_pending_d_PROC
assign xdlh_xmlh_pkt_pending_rising_edge    = xdlh_xmlh_pkt_pending & ~xdlh_xmlh_pkt_pending_d & ~cmd_is_data;
assign pkt_pending_detected_within_window_m = xdlh_xmlh_pkt_pending_rising_edge & pkt_pending_assert_window_m;
assign pkt_pending_detected_within_window_0 = xdlh_xmlh_pkt_pending_rising_edge & pkt_pending_assert_window_0;
assign pkt_pending_detected_within_window_1 = xdlh_xmlh_pkt_pending_rising_edge & pkt_pending_assert_window_1;
assign pkt_pending_detected_within_window_2 = xdlh_xmlh_pkt_pending_rising_edge & pkt_pending_assert_window_2;
always @(posedge core_clk or negedge core_rst_n) begin : pkt_pending_debug_PROC
    if ( ~core_rst_n ) begin
    end else begin
        if ( pkt_pending_detected_within_window_m )
            $display("%t %m: xdlh_xmlh_pkt_pending is asserted in window_m", $time); //entry to Rcvry.Lock
        if ( pkt_pending_detected_within_window_0 )
            $display("%t %m: xdlh_xmlh_pkt_pending is asserted in window_0", $time); //one cycle delay following entry to Rcvry.Lock
        if ( pkt_pending_detected_within_window_1 )
            $display("%t %m: xdlh_xmlh_pkt_pending is asserted in window_1", $time); //two cycles delay following entry to Rcvry.Lock
        if ( pkt_pending_detected_within_window_2 )
            $display("%t %m: xdlh_xmlh_pkt_pending is asserted in window_2", $time); //three cycles delay following entry to Rcvry.Lock
    end // if ( core_rst_n ) begin
end // pkt_pending_debug_PROC
//VCS coverage on
`endif // SYNTHESIS



`ifndef SYNTHESIS
`endif // !SYNTHESIS

endmodule
