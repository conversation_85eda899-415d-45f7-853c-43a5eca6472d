module next_compliance_state_sub (
    input wire S_COMPL_ENT_TX_EIDLE,
    input wire next_data_rate,
    input wire ts_sent_in_poll_active,
    output reg [N-1:0] next_compliance_state // TODO: 请根据实际宽度修改
);

    always @(curnt_compliance_state or ts_sent_in_poll_active or latched_eidle_sent
             or cfg_enter_compliance
             or send_mod_compliance
             or latched_enter_compliance or smlh_clr_enter_compliance
             or current_data_rate
             or rxelecidle_fall or next_data_rate or timeout_polling_eidle
             or pipe_regif_all_idle
        )
        begin : COMPLIANCE_STATE

                 case (curnt_compliance_state)
                S_COMPL_IDLE:
                    if (|next_data_rate) //have to send EIOS for gen2/3, then change speed
                        if (ts_sent_in_poll_active)                                 // if TSs were sent in Polling.Active EIOS must be sent
                            next_compliance_state   = S_COMPL_ENT_TX_EIDLE;


endmodule
